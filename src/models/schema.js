/* eslint-disable prettier/prettier */
// This file is auto-generated by the schema-generator.js script.
// Do not edit this file directly.
'use strict';
const { DataTypes, Sequelize } = require('../config/sequelize');

module.exports = {
  "allSchemas": {
    "bones": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "internal_job_profile_data": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "job_division": {
        "type": DataTypes.STRING
      },
      "job_group": {
        "type": DataTypes.STRING
      },
      "position_name": {
        "type": DataTypes.STRING
      },
      "job_classification": {
        "type": DataTypes.STRING
      },
      "job_family": {
        "type": DataTypes.STRING
      },
      "sub_job_family": {
        "type": DataTypes.STRING
      },
      "main_responsibilities": {
        "type": DataTypes.TEXT
      },
      "work_input": {
        "type": DataTypes.TEXT
      },
      "work_output": {
        "type": DataTypes.TEXT
      },
      "success_criteria": {
        "type": DataTypes.TEXT
      },
      "requirement": {
        "type": DataTypes.TEXT
      },
      "competency": {
        "type": DataTypes.TEXT
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "job_group_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "keywords": {
        "type": DataTypes.ARRAY(DataTypes.STRING),
        "allowNull": false,
        "defaultValue": []
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "description": {
        "type": DataTypes.TEXT
      },
      "order_level": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "deleted_at": {
        "type": DataTypes.DATE
      },
      "phrases": {
        "type": DataTypes.STRING
      },
      "jgv_code": {
        "type": DataTypes.STRING
      }
    },
    "job_levels": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "order_level": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "job_titles": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "unique": true
      },
      "prefilled_details": {
        "type": DataTypes.JSONB
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "job_vacancies": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "department": {
        "type": DataTypes.STRING
      },
      "job_grade": {
        "type": DataTypes.STRING
      },
      "job_description": {
        "type": DataTypes.TEXT
      },
      "job_title_id": {
        "type": DataTypes.INTEGER
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "job_desc": {
        "type": DataTypes.ARRAY(DataTypes.STRING),
        "allowNull": false,
        "defaultValue": []
      },
      "ksao": {
        "type": DataTypes.JSONB,
        "allowNull": false,
        "defaultValue": {
          "skills": [],
          "abilities": [],
          "knowledges": [],
          "other_characteristics": []
        }
      },
      "related_user_ids": {
        "type": DataTypes.ARRAY(DataTypes.INTEGER)
      },
      "related_onetsoc_codes": {
        "type": DataTypes.ARRAY(DataTypes.STRING)
      },
      "status": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "defaultValue": "draft"
      },
      "detailed_descriptions": {
        "type": DataTypes.JSON,
        "defaultValue": {}
      },
      "job_level_id": {
        "type": DataTypes.INTEGER
      },
      "role_summary": {
        "type": DataTypes.TEXT
      },
      "work_area_id": {
        "type": DataTypes.INTEGER
      },
      "bone_id": {
        "type": DataTypes.INTEGER
      },
      "relevant_working_experience": {
        "type": DataTypes.BOOLEAN
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "job_vacancy_pipelines": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "parameterized_name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "job_vacancy_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "order_level": {
        "type": DataTypes.INTEGER
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "job_vacancy_variable_benchmarks": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "job_vacancy_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "baseline_scale": {
        "type": DataTypes.INTEGER
      },
      "baseline_score": {
        "type": DataTypes.DOUBLE
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      },
      "configured_baseline_scale": {
        "type": DataTypes.INTEGER
      },
      "configured_baseline_score": {
        "type": DataTypes.DOUBLE
      }
    },
    "job_vacancy_work_areas": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "job_vacancy_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "work_area_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "job_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "job_group_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "name": {
        "type": DataTypes.STRING
      },
      "normalized_baseline": {
        "type": DataTypes.DOUBLE
      },
      "variable_type": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "filter_scales": {
        "type": DataTypes.JSONB,
        "allowNull": false,
        "defaultValue": {}
      },
      "deleted_at": {
        "type": DataTypes.DATE
      },
      "scoring_direction": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "defaultValue": "asc"
      },
      "mandatory": {
        "type": DataTypes.BOOLEAN,
        "allowNull": false,
        "defaultValue": false
      }
    },
    "llm_metadata": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "request": {
        "type": DataTypes.JSON
      },
      "responses": {
        "type": DataTypes.JSON
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "action_type": {
        "type": DataTypes.STRING
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "permissions": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "role_permissions": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "role_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "permission_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "roles": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_assessment_results": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER
      },
      "assessment": {
        "type": DataTypes.TEXT
      },
      "aspect_name": {
        "type": DataTypes.TEXT
      },
      "value_type": {
        "type": DataTypes.TEXT
      },
      "value": {
        "type": DataTypes.TEXT
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_bones": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "bone_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      },
      "value": {
        "type": DataTypes.DOUBLE
      }
    },
    "user_competencies_profilings": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER
      },
      "profiling_date": {
        "type": DataTypes.STRING
      },
      "assessors": {
        "type": DataTypes.ARRAY(DataTypes.STRING)
      },
      "profile_as": {
        "type": DataTypes.STRING
      },
      "readiness": {
        "type": DataTypes.STRING
      },
      "metadata": {
        "type": DataTypes.JSONB
      },
      "created_at": {
        "type": DataTypes.DATEONLY,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATEONLY,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_job_vacancies": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_vacancy_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "competency_match": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      },
      "skill_match": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      },
      "status": {
        "type": DataTypes.STRING
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "match_rate": {
        "type": DataTypes.DOUBLE
      },
      "deleted_at": {
        "type": DataTypes.DATE
      },
      "incomplete_variables": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "filtered_out_variable_scales": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      }
    },
    "user_job_vacancy_pipelines": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_vacancy_pipeline_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "created_by_id": {
        "type": DataTypes.INTEGER
      },
      "approved_by_id": {
        "type": DataTypes.INTEGER
      },
      "approved_at": {
        "type": DataTypes.DATE
      },
      "moved_to_next_pipeline_at": {
        "type": DataTypes.DATE
      },
      "note": {
        "type": DataTypes.TEXT
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_job_variable_constants": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_job_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "constant": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "match_score": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_job_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "raw_value": {
        "type": DataTypes.STRING
      },
      "normalized_value": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_performance_reviews": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_position_id": {
        "type": DataTypes.INTEGER
      },
      "review_type": {
        "type": DataTypes.STRING
      },
      "review_result": {
        "type": DataTypes.JSONB
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_positions": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "role_name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "department": {
        "type": DataTypes.STRING
      },
      "job_grade": {
        "type": DataTypes.STRING
      },
      "starts_at": {
        "type": DataTypes.DATEONLY
      },
      "ends_at": {
        "type": DataTypes.DATEONLY
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "company_name": {
        "type": DataTypes.STRING
      },
      "work_area": {
        "type": DataTypes.STRING
      },
      "division": {
        "type": DataTypes.STRING
      },
      "directorate": {
        "type": DataTypes.STRING
      },
      "grade_group": {
        "type": DataTypes.STRING
      },
      "last_education": {
        "type": DataTypes.STRING
      },
      "major": {
        "type": DataTypes.STRING
      },
      "sub_grade": {
        "type": DataTypes.STRING
      },
      "employee_id": {
        "type": DataTypes.STRING
      },
      "supervisor_id": {
        "type": DataTypes.INTEGER
      },
      "transition_type": {
        "type": DataTypes.STRING
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_profiles": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "unique": true
      },
      "phone_number": {
        "type": DataTypes.STRING
      },
      "location": {
        "type": DataTypes.STRING
      },
      "manager": {
        "type": DataTypes.STRING
      },
      "current_position": {
        "type": DataTypes.JSONB
      },
      "years_experience": {
        "type": DataTypes.INTEGER
      },
      "performance_rating": {
        "type": DataTypes.DOUBLE
      },
      "last_promotion": {
        "type": DataTypes.DATEONLY
      },
      "education": {
        "type": DataTypes.STRING
      },
      "competencies": {
        "type": DataTypes.ARRAY(DataTypes.STRING)
      },
      "skills": {
        "type": DataTypes.ARRAY(DataTypes.STRING)
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_vacancy_group_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "vacancy_group_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "average_match_score": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "user_vacancy_variable_scores": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_vacancy_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "match_score": {
        "type": DataTypes.DOUBLE
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    },
    "users": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "email": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "unique": true
      },
      "password_digest": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "role": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "defaultValue": "user"
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "join_date": {
        "type": DataTypes.DATEONLY
      },
      "employment_status": {
        "type": DataTypes.STRING
      },
      "deleted_at": {
        "type": DataTypes.DATE
      },
      "supervisor_id": {
        "type": DataTypes.INTEGER
      },
      "hierarchy_path": {
        "type": DataTypes.LTREE
      }
    },
    "vacancy_group_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "job_vacancy_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_group_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "keyword_match_count": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "match_type": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "defaultValue": "filter"
      },
      "weight": {
        "type": DataTypes.DOUBLE
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "filters": {
        "type": DataTypes.JSONB,
        "allowNull": false,
        "defaultValue": {}
      },
      "keyword_total_count": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "bone_value": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "deleted_at": {
        "type": DataTypes.DATE
      },
      "default_weight": {
        "type": DataTypes.DOUBLE
      },
      "avg_baseline_scale": {
        "type": DataTypes.INTEGER
      },
      "configured_baseline_scale": {
        "type": DataTypes.INTEGER
      }
    },
    "work_areas": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "deleted_at": {
        "type": DataTypes.DATE
      }
    }
  },
  "allIndexes": {
    "bones": [
      {
        "name": "bones_name_unique_constraint",
        "fields": [
          "name"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "internal_job_profile_data": [
      {
        "name": "internal_job_profile_data_position_name_index",
        "fields": [
          "position_name"
        ],
        "using": "btree"
      }
    ],
    "job_group_variables": [
      {
        "name": "job_group_variables_jgv_code_unique_constraint",
        "fields": [
          "jgv_code"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "job_titles": [
      {
        "name": "job_titles_name_key",
        "fields": [
          "name"
        ],
        "unique": true,
        "using": "btree"
      },
      {
        "name": "job_titles_name_unique_constraint",
        "fields": [
          "name"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "job_vacancies": [
      {
        "name": "job_vacancies_job_title_id_index",
        "fields": [
          "job_title_id"
        ],
        "using": "btree"
      }
    ],
    "job_vacancy_pipelines": [
      {
        "name": "job_vacancy_pipelines_job_vacancy_id_index",
        "fields": [
          "job_vacancy_id"
        ],
        "using": "btree"
      },
      {
        "name": "job_vacancy_pipelines_order_level_index",
        "fields": [
          "order_level"
        ],
        "using": "btree"
      },
      {
        "name": "job_vacancy_pipelines_parameterized_name_job_vacancy_id_unique",
        "fields": [
          "parameterized_name",
          "job_vacancy_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "job_vacancy_variable_benchmarks": [
      {
        "name": "job_vacancy_variable_benchmarks_jv_id_jv_id_unique_constraint",
        "fields": [
          "job_vacancy_id",
          "job_variable_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "job_vacancy_work_areas": [
      {
        "name": "job_vacancy_work_areas_jv_id_wa_id_unique_constraint",
        "fields": [
          "job_vacancy_id",
          "work_area_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "job_variables": [
      {
        "name": "job_variables_job_group_variable_id",
        "fields": [
          "job_group_variable_id"
        ],
        "using": "btree"
      },
      {
        "name": "job_variables_name_jgv_id_unique_constraint",
        "fields": [
          "name",
          "job_group_variable_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "permissions": [
      {
        "name": "permissions_name_unique_constraint",
        "fields": [
          "name"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "role_permissions": [
      {
        "name": "role_permissions_role_id_permission_id_unique_constraint",
        "fields": [
          "role_id",
          "permission_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "roles": [
      {
        "name": "roles_name_unique_constraint",
        "fields": [
          "name"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "user_bones": [
      {
        "name": "user_bones_bone_id_index",
        "fields": [
          "bone_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_bones_user_id_index",
        "fields": [
          "user_id"
        ],
        "using": "btree"
      }
    ],
    "user_competencies_profilings": [
      {
        "name": "user_competencies_profilings_user_id",
        "fields": [
          "user_id"
        ],
        "using": "btree"
      }
    ],
    "user_job_vacancies": [
      {
        "name": "user_job_vacancies_job_vacancy_id",
        "fields": [
          "job_vacancy_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_job_vacancies_status",
        "fields": [
          "status"
        ],
        "using": "btree"
      },
      {
        "name": "user_job_vacancies_user_id",
        "fields": [
          "user_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_job_vacancies_user_id_job_vacancy_id_unique_constraint",
        "fields": [
          "user_id",
          "job_vacancy_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "user_job_vacancy_pipelines": [
      {
        "name": "user_job_vacancy_pipelines_approved_by_id_index",
        "fields": [
          "approved_by_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_job_vacancy_pipelines_created_by_id_index",
        "fields": [
          "created_by_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_job_vacancy_pipelines_job_vacancy_pipeline_id_index",
        "fields": [
          "job_vacancy_pipeline_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_job_vacancy_pipelines_user_id_index",
        "fields": [
          "user_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_job_vacancy_pipelines_user_id_job_vacancy_pipeline_id_uniq",
        "fields": [
          "user_id",
          "job_vacancy_pipeline_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "user_job_variable_constants": [
      {
        "name": "user_job_variable_constants_ujv_id_constant_unique_constraint",
        "fields": [
          "user_job_variable_id",
          "constant"
        ],
        "unique": true,
        "using": "btree"
      },
      {
        "name": "user_job_variable_constants_user_job_variable_id",
        "fields": [
          "user_job_variable_id"
        ],
        "using": "btree"
      }
    ],
    "user_job_variables": [
      {
        "name": "user_job_variables_job_variable_id",
        "fields": [
          "job_variable_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_job_variables_user_id",
        "fields": [
          "user_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_job_variables_user_id_jv_id_unique_constraint",
        "fields": [
          "user_id",
          "job_variable_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "user_positions": [
      {
        "name": "user_positions_user_id",
        "fields": [
          "user_id"
        ],
        "using": "btree"
      }
    ],
    "user_profiles": [
      {
        "name": "user_profiles_user_id",
        "fields": [
          "user_id"
        ],
        "using": "btree"
      },
      {
        "name": "user_profiles_user_id_key",
        "fields": [
          "user_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "user_vacancy_group_variables": [
      {
        "name": "user_vacancy_group_variables_user_id_vgv_id_unique_constraint",
        "fields": [
          "user_id",
          "vacancy_group_variable_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "user_vacancy_variable_scores": [
      {
        "name": "user_vacancy_variable_scores_uid_jvac_id_jvar_id_unique_constra",
        "fields": [
          "user_id",
          "job_vacancy_id",
          "job_variable_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "users": [
      {
        "name": "users_email_key",
        "fields": [
          "email"
        ],
        "unique": true,
        "using": "btree"
      },
      {
        "name": "users_email_unique_constraint",
        "fields": [
          "email"
        ],
        "unique": true,
        "using": "btree"
      },
      {
        "name": "users_hierarchy_path_index",
        "fields": [
          "hierarchy_path"
        ],
        "using": "gist"
      },
      {
        "name": "users_supervisor_id_index",
        "fields": [
          "supervisor_id"
        ],
        "using": "btree"
      }
    ],
    "vacancy_group_variables": [
      {
        "name": "vacancy_group_variables_vacancy_id_jgv_id_unique_constraint",
        "fields": [
          "job_vacancy_id",
          "job_group_variable_id"
        ],
        "unique": true,
        "using": "btree"
      }
    ],
    "work_areas": [
      {
        "name": "work_areas_name_unique_constraint",
        "fields": [
          "name"
        ],
        "unique": true,
        "using": "btree"
      }
    ]
  },
  "appliedMigrations": [
    "20250811230230-create-users.js",
    "20250815023405-create-job-titles.js",
    "20250815031343-create-job-vacancies.js",
    "20250815041927-create-user-profiles.js",
    "20250815041931-create-user-positions.js",
    "20250826010440-create-user-job-vacancies.js",
    "20250830011852-add-ksao-to-job-vacancies.js",
    "20250830024449-create-internal-job-profile-data.js",
    "20250830034656-change-column-type-work-output-internal-job-data.js",
    "20250830040838-create-job-group-variables.js",
    "20250830041032-create-job-variables.js",
    "20250830041427-create-vacancy-group-variables.js",
    "20250830041844-create-user-job-variables.js",
    "20250830041959-create-user-vacancy-group-variables.js",
    "20250830042048-add-match-rate-to-user-job-vacancies.js",
    "20250830045405-create-user-competencies-profiling.js",
    "20250830051125-create-user-performance-review.js",
    "20250830053632-create-user-assessment-result.js",
    "20250830074740-add-unique-constraint-user-job-vacancies.js",
    "20250830074825-change-allow-null-job-title-id-to-job-vancancies.js",
    "20250830093335-add-column-related-user-ids-and-onetsoc-codes-to-job-vacancies.js",
    "20250831013044-add-status-to-job-vacancies.js",
    "20250831013751-change-user-job-vacancies-status-column.js",
    "20250831014329-add-name-to-job-variables.js",
    "20250831014431-add-description-and-order-level-to-job-group-variables.js",
    "20250831014533-add-filters-to-vacancy-group-variables.js",
    "20250831035136-add-keyword-total-count-to-vgv.js",
    "20250831045740-change-job-vacancies-department-and-job-grades-null.js",
    "20250831051024-drop-competencies-skills-from-job-vacancies.js",
    "20250831073243-modify-some-table-fields-for-spreadsheets.js",
    "20250831085902-rename-createdat-updatedat-to-snake-case.js",
    "20250831140257-change-some-user-job-variable-columns.js",
    "20250831140512-create-user-job-variable-constants.js",
    "20250831150554-remove-unused-columns-from-job-variables.js",
    "20250831151155-change-some-job-variables-columns.js",
    "20250831152152-add-unique-job-variables-name.js",
    "20250831152200-add-unique-user-job-variables.js",
    "20250831161419-change-job-variables-unique-constraint.js",
    "20250901051252-add-more-fields-to-user-positions.js",
    "20250901070156-add-employee-id-to-user-positions.js",
    "20250902022643-add-column-metadata-to-job-vacancies.js",
    "20250902092858-change-metadata-column-name-to-detailed-desriptions-in-job-vacancies.js",
    "20250902102703-create-job-levels.js",
    "20250902102800-add-job-level-id-to-job-vacancies.js",
    "20250904024522-create-llm-metadata-table.js",
    "20250904024838-add-column-action-type-to-llm-metadata.js",
    "20250904043520-rename-timestamps-on-llm-metadata.js",
    "20250904075333-add-fields-to-users.js",
    "20250904080140-add-fields-to-user-positions.js",
    "20250909020938-add-role-summary-to-job-vacancies.js",
    "20250910035154-create-user-vacancy-variable-scores.js",
    "20250910072252-change-column-null-on-user-vacancy-variable-scores-match-score.js",
    "20250910092445-create-work-areas.js",
    "20250910092808-create-bones.js",
    "20250910092910-add-new-columns-to-job-vacancies.js",
    "20250910095815-create-user-bones.js",
    "20250911023902-add-filter-scales-to-job-variables.js",
    "20250911031843-add-deleted-at-to-job-vacancies.js",
    "20250911050545-add-bone-value-to-vacancy-group-variables.js",
    "20250911072455-add-deleted-at-to-all-tables.js",
    "20250911100707-add-value-to-user-bones.js",
    "20250912031015-create-job-vacancy-work-areas.js",
    "20250916000000-add-default-weight-to-vacancy-group-variables.js",
    "20250916042652-add-phrases-and-jgv-code-to-job-group-variables.js",
    "20250918063630-enable-ltree-extension.js",
    "20250918063713-add-supervisor-and-hierarchy-path-to-users.js",
    "20250923003907-create-job-vacancy-variable-benchmarks.js",
    "20250923004514-add-avg-baseline-scale-to-vgv.js",
    "20250923021343-add-scoring-direction-to-job-variables.js",
    "20250930064711-add-configured-baseline-scale-to-vacancy-group-variables.js",
    "20250930064759-add-configured-baseline-scale-and-score-to-job-vacancy-variable-benchmarks.js",
    "20250930064952-add-mandatory-to-job-variables.js",
    "20251002011214-create-roles.js",
    "20251002011310-create-permissions.js",
    "20251002011346-create-role-permissions.js",
    "20251002073343-add-unique-constraint-role-permissions.js",
    "20251006020236-add-count-columns-to-user-job-vacancies.js",
    "20251007004111-create-job-vacancy-pipelines.js",
    "20251007004119-create-user-job-vacancy-pipelines.js"
  ]
};
