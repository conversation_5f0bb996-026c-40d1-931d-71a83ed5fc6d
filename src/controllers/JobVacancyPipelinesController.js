const ApiController = require('./ApiController');
const JobVacancyPipelineService = require('../services/JobVacancyPipelineService');
const JobVacancyPipelinesIndexInput = require('../inputs/JobVacancyPipelinesIndexInput');
const JobVacancyPipelineOutput = require('../outputs/JobVacancyPipelineOutput');

class JobVacancyPipelinesController extends ApiController {
  constructor() {
    super();
    this.service = new JobVacancyPipelineService();
  }

  /**
   * GET /api/v1/job_vacancies/:id/pipelines
   * Get all pipelines for a specific job vacancy
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    // Get job_vacancy_id from URL params
    const jobVacancyId = parseInt(req.params.id, 10);

    // Merge URL params with query params
    const queryParams = {
      ...req.query,
      job_vacancy_id: jobVacancyId,
    };

    const input = new JobVacancyPipelinesIndexInput(queryParams);
    input.validate();

    const result = await this.service.findAll(input.output());
    const output = new JobVacancyPipelineOutput(result.jobVacancyPipelines, {
      pagination: result.pagination,
    });

    output.renderJsonArray(res);
  });
}

module.exports = new JobVacancyPipelinesController();
