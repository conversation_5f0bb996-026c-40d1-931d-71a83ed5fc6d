require('dotenv').config({ path: '.env.test', quiet: true });
const { sequelize } = require('../src/models');
const IORedisMock = require('ioredis-mock');

const {
  beforeAllBase,
  beforeEachBase,
  afterEachBase,
  afterAllBase,
} = require('./utils/dbCleanerHooks');

// Set test environment
process.env.NODE_ENV = 'test';

const QdrantServiceMock = require('./mocks/QdrantServiceMock');
const GoogleAiServiceMock = require('./mocks/GoogleAiServiceMock');

// Mock Qdrant client globally to avoid connection failures
jest.mock('@qdrant/js-client-rest', () => {
  return {
    QdrantClient: jest.fn().mockImplementation(() => new QdrantServiceMock()),
  };
});

// Mock Google AI service globally to avoid expensive API calls
jest.mock('@google/genai', () => {
  return {
    GoogleGenAI: jest.fn().mockImplementation(() => new GoogleAiServiceMock()),
  };
});

// Mock ioredis to prevent Redis connections
jest.mock('ioredis', () => IORedisMock);

// Mock BullMQ globally to prevent any job enqueuing in tests
jest.mock('bullmq', () => ({
  Queue: jest.fn().mockImplementation(() => ({
    add: jest.fn().mockResolvedValue({ id: `job-${Date.now()}` }),
    close: jest.fn().mockResolvedValue(undefined),
  })),
  Worker: jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    close: jest.fn().mockResolvedValue(undefined),
  })),
}));

// Mock Bull Board components to prevent issues in app.js
jest.mock('@bull-board/api', () => ({
  createBullBoard: jest.fn(() => ({})),
  BullMQAdapter: jest.fn().mockImplementation(() => ({})),
}));

jest.mock('@bull-board/express', () => ({
  ExpressAdapter: jest.fn().mockImplementation(() => ({
    setBasePath: jest.fn(),
    getRouter: jest.fn(() => (_req, _res, next) => next()),
  })),
}));

jest.mock('../src/middlewares/bullboard', () => ({
  bullBoardAuth: jest.fn((req, res, next) => next()),
  bullBoardServer: {
    getRouter: jest.fn(() => (_req, _res, next) => next()),
  },
}));

// Mock OnetService to avoid database queries to non-existent onet_occupations table
jest.mock('../src/services/OnetService', () => {
  return class OnetService {
    async getOccupations(_onetsocCodes) {
      // Mock implementation that returns sample occupation data
      return [
        {
          onetsoc_code: '15-1252.00',
          title: 'Software Developers, Applications',
          description:
            'Develop, create, and modify general computer applications software or specialized utility programs.',
        },
        {
          onetsoc_code: '15-1251.00',
          title: 'Computer Programmers',
          description:
            'Create, modify, and test the code and scripts that allow computer applications to run.',
        },
      ];
    }

    async getTasks(_onetsocCodes) {
      // Mock implementation that returns sample task data
      return [
        {
          onetsoc_code: '15-1252.00',
          task: 'Analyze user requirements to derive technical software design and performance requirements.',
        },
        {
          onetsoc_code: '15-1252.00',
          task: 'Debug, maintain, and update existing software applications.',
        },
        {
          onetsoc_code: '15-1251.00',
          task: 'Write, update, and maintain computer programs or software packages.',
        },
      ];
    }
  };
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test timeout
jest.setTimeout(3000);

// Prevent perform_async to add jobs to the queue
global.performAsyncSpy = null;
const ApplicationJob = require('../src/jobs/ApplicationJob');

beforeEach(async () => {
  await beforeEachBase();

  global.performAsyncSpy = jest
    .spyOn(ApplicationJob, 'perform_async')
    .mockResolvedValue({ id: 'job-123' });
});

afterEach(async () => {
  await afterEachBase();
  jest.clearAllMocks();
  global.performAsyncSpy?.mockRestore();
});

beforeAll(async () => {
  await beforeAllBase();
});

afterAll(async () => {
  await afterAllBase();

  try {
    await sequelize.close();
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
});
