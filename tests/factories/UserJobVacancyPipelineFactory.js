'use strict';

const BaseFactory = require('./BaseFactory');
const { UserJobVacancyPipeline } = require('../../src/models');

class UserJobVacancyPipelineFactory extends BaseFactory {
  constructor() {
    super(UserJobVacancyPipeline);
  }

  static defaultAttributes() {
    return {
      user_id: 1, // Will be overridden in tests
      job_vacancy_pipeline_id: 1, // Will be overridden in tests
      created_by_id: null,
      approved_by_id: null,
      approved_at: null,
      moved_to_next_pipeline_at: null,
      note: this.faker.lorem.sentence(),
    };
  }

  static traits() {
    return {
      approved: {
        approved_by_id: 1,
        approved_at: new Date(),
        note: 'Approved for next stage',
      },
      moved: {
        moved_to_next_pipeline_at: new Date(),
        note: 'Moved to next pipeline stage',
      },
      with_notes: {
        note: 'Detailed notes about the candidate performance and feedback',
      },
    };
  }
}

module.exports = UserJobVacancyPipelineFactory;
