const SetVacancyGroupVariablesJob = require('../../src/jobs/SetVacancyGroupVariablesJob');
const {
  JobVacancyFactory,
  JobGroupVariableFactory,
  JobVariableFactory,
  UserFactory,
  UserJobVariableFactory,
} = require('../factories');
const { JobVacancyVariableBenchmark } = require('../../src/models');

// Mock external services
jest.mock('../../src/services/OnetService');
jest.mock('../../src/services/external/GoogleAiService');

describe('SetVacancyGroupVariablesJob', () => {
  let job;
  let vacancy;
  let jobGroupVariable;
  let jobVariable;
  let userBenchmark;

  beforeEach(async () => {
    jobGroupVariable = await JobGroupVariableFactory.create({
      name: 'Test Group Variable',
      description: 'Test description',
      keywords: ['javascript', 'react', 'node'],
      order_level: 1,
    });

    jobVariable = await JobVariableFactory.create({
      name: 'Test Variable',
      job_group_variable_id: jobGroupVariable.id,
      variable_type: 'numeric',
      filter_scales: { 1: 10, 2: 20, 3: 30, 4: 40, 5: 50, 6: 60, 7: 70 },
      mandatory: false,
    });

    userBenchmark = await UserFactory.create();
    await UserJobVariableFactory.create({
      user_id: userBenchmark.id,
      job_variable_id: jobVariable.id,
      raw_value: '5',
      normalized_value: 5,
    });

    vacancy = await JobVacancyFactory.create({
      name: 'Software Engineer',
      status: 'generating_job_variables',
      related_user_ids: [userBenchmark.id],
      related_onetsoc_codes: ['15-1252.00'],
      detailed_descriptions: {
        key_responsibilities: ['Develop software', 'Write tests'],
        qualifications: ['Bachelor degree', '3 years experience'],
        competencies: ['Problem solving', 'Teamwork'],
        success_metrics: ['Code quality', 'Delivery speed'],
      },
    });

    job = new SetVacancyGroupVariablesJob();
    job.generateVgvService.generateKsao = jest.fn().mockResolvedValue({
      knowledges: ['Software Development', 'Testing'],
      skills: ['Programming', 'Debugging'],
      abilities: ['Problem Solving', 'Analytical Thinking'],
      other_characteristics: ['Detail Oriented', 'Team Player'],
    });
  });

  describe('Successful Job Execution', () => {
    it('should update vacancy status to draft', async () => {
      await job.perform({ vacancy_id: vacancy.id });
      await vacancy.reload();
      expect(vacancy.status).toBe('draft');
    });

    it('should skip KSAO generation when config is disabled', async () => {
      job.config.vacancyGroupVariable.generateKsao = false;

      await job.perform({ vacancy_id: vacancy.id });
      expect(job.generateVgvService.generateKsao).not.toHaveBeenCalled();

      await vacancy.reload();
      expect(vacancy.status).toBe('draft');
    });

    it('should generate jvvb records correctly', async () => {
      await job.perform({ vacancy_id: vacancy.id });

      const jvvbs = await JobVacancyVariableBenchmark.findAll({
        where: { job_vacancy_id: vacancy.id },
      });

      expect(jvvbs).toHaveLength(1);
      expect(jvvbs[0].job_variable_id).toBe(jobVariable.id);
      expect(jvvbs[0].baseline_score).toBe(5);
      expect(jvvbs[0].baseline_scale).toBe(5);
      expect(jvvbs[0].configured_baseline_score).toBe(5);
      expect(jvvbs[0].configured_baseline_scale).toBe(5);
    });

    it('should use avg when there is two benchmarks', async () => {
      const userBenchmark2 = await UserFactory.create();
      await UserJobVariableFactory.create({
        user_id: userBenchmark2.id,
        job_variable_id: jobVariable.id,
        raw_value: '2',
        normalized_value: 2,
      });

      await vacancy.update({
        related_user_ids: [userBenchmark.id, userBenchmark2.id],
      });

      await job.perform({ vacancy_id: vacancy.id });
      const jvvbs = await JobVacancyVariableBenchmark.findAll({
        where: { job_vacancy_id: vacancy.id },
      });

      expect(jvvbs).toHaveLength(1);
      expect(jvvbs[0].job_variable_id).toBe(jobVariable.id);
      // 2+5 = 7 / 2 = 3.5 => 4
      expect(jvvbs[0].baseline_score).toBe(4);
      expect(jvvbs[0].baseline_scale).toBe(4);
      expect(jvvbs[0].configured_baseline_score).toBe(4);
      expect(jvvbs[0].configured_baseline_scale).toBe(4);
    });

    it('should use median when there is three benchmarks', async () => {
      const userBenchmark2 = await UserFactory.create();
      await UserJobVariableFactory.create({
        user_id: userBenchmark2.id,
        job_variable_id: jobVariable.id,
        raw_value: '1',
        normalized_value: 1,
      });

      const userBenchmark3 = await UserFactory.create();
      await UserJobVariableFactory.create({
        user_id: userBenchmark3.id,
        job_variable_id: jobVariable.id,
        raw_value: '2',
        normalized_value: 2,
      });

      await vacancy.update({
        related_user_ids: [userBenchmark.id, userBenchmark2.id, userBenchmark3.id],
      });

      await job.perform({ vacancy_id: vacancy.id });
      const jvvbs = await JobVacancyVariableBenchmark.findAll({
        where: { job_vacancy_id: vacancy.id },
      });

      expect(jvvbs).toHaveLength(1);
      expect(jvvbs[0].job_variable_id).toBe(jobVariable.id);
      // 1, 2, 5 => 2
      expect(jvvbs[0].baseline_score).toBe(2);
      expect(jvvbs[0].baseline_scale).toBe(2);
      expect(jvvbs[0].configured_baseline_score).toBe(2);
      expect(jvvbs[0].configured_baseline_scale).toBe(2);
    });
  });

  describe('Error Handling', () => {
    it('should handle KSAO generation error and still update status', async () => {
      job.generateVgvService.generateKsao.mockRejectedValue(new Error('KSAO generation failed'));
      await job.perform({ vacancy_id: vacancy.id });

      await vacancy.reload();
      expect(vacancy.status).toBe('draft');
    });

    it('should handle missing vacancy', async () => {
      await expect(job.perform({ vacancy_id: 999999 })).rejects.toThrow();
    });
  });
});
