const express = require('express');
const resources = require('./resources');
const vgvController = require('../controllers/VacancyGroupVariablesController');
const { authenticateToken, requirePermission } = require('../middlewares/auth');

const router = express.Router();
router.use(authenticateToken);

router.use('/', resources('vacancy_group_variables', vgvController, { only: ['index'] }));

router.patch(
  '/bulk_update',
  requirePermission('vacancy_group_variables:bulk_update'),
  vgvController.bulkUpdate,
);

module.exports = router;
