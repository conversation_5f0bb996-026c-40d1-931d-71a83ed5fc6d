const config = require('../config/config');
const InvalidError = require('../errors/InvalidError');
const NotFoundError = require('../errors/NotFoundError');

class AppService {
  constructor() {
    this.config = config;
    this.permissions = [];
  }

  assert(condition, message) {
    if (!condition) {
      throw new InvalidError(message);
    }
  }

  exists(object, message) {
    if (!object) {
      throw new NotFoundError(message);
    }
  }

  setPermissions(permissions) {
    this.permissions = permissions || [];
  }

  hasPermission(permission) {
    return this.permissions.includes(permission);
  }
}

module.exports = AppService;
