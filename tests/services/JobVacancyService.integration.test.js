const JobVacancyService = require('../../src/services/JobVacancyService');
const { JobVacancy, JobTitle } = require('../../src/models');
const SetJobDescriptionJob = require('../../src/jobs/SetJobDescriptionJob');
const SetVacancyGroupVariablesJob = require('../../src/jobs/SetVacancyGroupVariablesJob');

// Mock dependencies
jest.mock('../../src/models');
jest.mock('../../src/repositories/JobVacanciesRepository');
jest.mock('../../src/services/OnetService');
jest.mock('../../src/services/JobVacancy/GenerateJobDescService');
jest.mock('../../src/services/external/GoogleAiService');
jest.mock('@qdrant/js-client-rest');

describe('JobVacancyService - Job Integration', () => {
  let service;
  let mockVacancy;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock vacancy
    mockVacancy = {
      id: 1,
      name: 'Software Engineer',
      status: 'draft',
      update: jest.fn().mockResolvedValue(true),
    };

    // Mock JobVacancy.create
    JobVacancy.create = jest.fn().mockResolvedValue(mockVacancy);

    // Mock JobTitle.findByPk
    JobTitle.findByPk = jest.fn().mockResolvedValue({
      name: 'Software Engineer',
    });

    service = new JobVacancyService();

    // Mock repository
    service.repository = {
      findOne: jest.fn().mockResolvedValue(mockVacancy),
    };
  });

  describe('create', () => {
    it('should enqueue SetJobDescriptionJob after creating vacancy', async () => {
      const data = {
        name: 'Software Engineer',
        job_title_id: 1,
        followup_action: 'generate_jobdesc',
      };

      const result = await service.create(data);

      expect(JobVacancy.create).toHaveBeenCalledWith({
        name: 'Software Engineer',
        job_title_id: 1,
        status: 'generating_jobdesc',
      });

      expect(global.performAsyncSpy).toHaveBeenCalledWith({
        vacancy_id: 1,
      });

      const calledJob = global.performAsyncSpy.mock.instances[0];
      expect(calledJob).toBe(SetJobDescriptionJob);

      expect(result).toBe(mockVacancy);
    });

    it('should use job title name when name is not provided', async () => {
      const data = {
        job_title_id: 1,
        followup_action: 'generate_jobdesc',
      };

      await service.create(data);

      expect(JobTitle.findByPk).toHaveBeenCalledWith(1);
      expect(JobVacancy.create).toHaveBeenCalledWith({
        name: 'Software Engineer',
        job_title_id: 1,
        status: 'generating_jobdesc',
      });

      expect(global.performAsyncSpy).toHaveBeenCalledWith({
        vacancy_id: 1,
      });

      const calledJob = global.performAsyncSpy.mock.instances[0];
      expect(calledJob).toBe(SetJobDescriptionJob);
    });

    it('should handle job enqueueing errors gracefully', async () => {
      SetJobDescriptionJob.perform_async.mockRejectedValue(new Error('Queue error'));

      const data = {
        name: 'Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      // Should still create the vacancy even if job enqueueing fails
      await expect(service.create(data)).rejects.toThrow('Queue error');

      expect(JobVacancy.create).toHaveBeenCalled();
    });
  });

  describe('update', () => {
    beforeEach(() => {
      mockVacancy.status = 'draft';
    });

    it('should enqueue SetJobDescriptionJob for generate_jobdesc followup action', async () => {
      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      const result = await service.update(1, data);

      expect(mockVacancy.update).toHaveBeenCalledWith({
        name: 'Updated Software Engineer',
        status: 'generating_jobdesc',
      });

      expect(global.performAsyncSpy).toHaveBeenCalledWith({
        vacancy_id: 1,
      });

      const calledJob = global.performAsyncSpy.mock.instances[0];
      expect(calledJob).toBe(SetJobDescriptionJob);

      expect(result).toBe(mockVacancy);
    });

    it('should enqueue SetVacancyGroupVariablesJob for generate_job_variables followup action', async () => {
      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_job_variables',
      };

      await service.update(1, data);

      expect(mockVacancy.update).toHaveBeenCalledWith({
        name: 'Updated Software Engineer',
        status: 'generating_job_variables',
      });

      expect(global.performAsyncSpy).toHaveBeenCalledWith({
        vacancy_id: 1,
      });

      const calledJob = global.performAsyncSpy.mock.instances[0];
      expect(calledJob).toBe(SetVacancyGroupVariablesJob);
    });

    it('should not enqueue jobs when no followup action is specified', async () => {
      const data = {
        name: 'Updated Software Engineer',
      };

      await service.update(1, data);

      expect(global.performAsyncSpy).not.toHaveBeenCalled();
    });

    it('should handle job enqueueing errors gracefully', async () => {
      SetJobDescriptionJob.perform_async.mockRejectedValue(new Error('Queue error'));

      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      // Should still update the vacancy even if job enqueueing fails
      await expect(service.update(1, data)).rejects.toThrow('Queue error');

      expect(mockVacancy.update).toHaveBeenCalled();
    });

    it('should validate vacancy status before allowing followup actions', async () => {
      mockVacancy.status = 'generating_jobdesc';

      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      await expect(service.update(1, data)).rejects.toThrow(
        'Cannot update this vacancy, current status: generating_jobdesc',
      );

      expect(global.performAsyncSpy).not.toHaveBeenCalled();
    });

    it('should allow followup actions for active status', async () => {
      mockVacancy.status = 'active';

      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      await service.update(1, data);

      expect(global.performAsyncSpy).toHaveBeenCalledWith({
        vacancy_id: 1,
      });

      const calledJob = global.performAsyncSpy.mock.instances[0];
      expect(calledJob).toBe(SetJobDescriptionJob);
    });
  });

  describe('job priority and options', () => {
    it('should enqueue jobs with default priority', async () => {
      const data = {
        name: 'Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      await service.create(data);

      expect(global.performAsyncSpy).toHaveBeenCalledWith(
        { vacancy_id: 1 },
        // No options passed, so default priority should be used
      );
    });
  });

  describe('error scenarios', () => {
    it('should handle database errors during vacancy creation', async () => {
      JobVacancy.create.mockRejectedValue(new Error('Database error'));

      const data = {
        name: 'Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      await expect(service.create(data)).rejects.toThrow('Database error');

      // Job should not be enqueued if vacancy creation fails
      expect(global.performAsyncSpy).not.toHaveBeenCalled();
    });

    it('should handle database errors during vacancy update', async () => {
      mockVacancy.update.mockRejectedValue(new Error('Database error'));

      const data = {
        name: 'Updated Software Engineer',
        followup_action: 'generate_jobdesc',
      };

      await expect(service.update(1, data)).rejects.toThrow('Database error');

      // Job should not be enqueued if vacancy update fails
      expect(global.performAsyncSpy).not.toHaveBeenCalled();
    });
  });
});
