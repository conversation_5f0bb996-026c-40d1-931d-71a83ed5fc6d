const AppService = require('./AppService');
const JobVacancyPipelinesRepository = require('../repositories/JobVacancyPipelinesRepository');
const { JobVacancy } = require('../models');

class JobVacancyPipelineService extends AppService {
  constructor() {
    super();
    this.repository = new JobVacancyPipelinesRepository();
  }

  /**
   * Get all job vacancy pipelines for a specific job vacancy
   * @param {Object} params - Query params (job_vacancy_id, page, limit, filters, etc.)
   * @returns {Object} Job vacancy pipelines array and pagination info
   */
  async findAll(params = {}) {
    // Validate that job vacancy exists
    const jobVacancy = await JobVacancy.findByPk(params.job_vacancy_id);
    this.exists(jobVacancy, 'Job vacancy not found');

    // Set default sorting by order_level
    params['sort'] = params.sort || 'order_level';
    params['sort_direction'] = params.sort_direction || 'asc';

    const { rows, pagination } = await this.repository.findAll(params);

    return { jobVacancyPipelines: rows, pagination };
  }

  /**
   * Find a job vacancy pipeline by ID
   * @param {number} id - Job vacancy pipeline ID
   * @returns {Object} Job vacancy pipeline
   * @throws {NotFoundError} If pipeline is not found
   */
  async findById(id) {
    const pipeline = await this.repository.model.findByPk(id);
    this.exists(pipeline, 'Job vacancy pipeline not found');
    return pipeline;
  }
}

module.exports = JobVacancyPipelineService;
