const express = require('express');
const { isRedisConnected } = require('./config/redis');
const { metricsHandler } = require('./middlewares/worker-prometheus');

/**
 * Simple HTTP health check server for worker pods
 * This allows Kubernetes to perform readiness and liveness probes
 */
class WorkerHealthServer {
  constructor(worker, port = 3001) {
    this.worker = worker;
    this.port = port;
    this.app = express();
    this.server = null;
    this.isShuttingDown = false;

    this.setupRoutes();
  }

  /**
   * Setup health check routes
   */
  setupRoutes() {
    // Basic health check endpoint
    this.app.get('/health', async (req, res) => {
      try {
        const health = await this.worker.healthCheck();
        const redisConnected = isRedisConnected();

        const isHealthy = health.status === 'healthy' && redisConnected && !this.isShuttingDown;

        res.status(isHealthy ? 200 : 503).json({
          ...health,
          redis: {
            connected: redisConnected,
          },
          server: {
            shuttingDown: this.isShuttingDown,
          },
        });
      } catch (error) {
        res.status(503).json({
          status: 'unhealthy',
          error: error.message,
          server: {
            shuttingDown: this.isShuttingDown,
          },
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Readiness probe - checks if worker is ready to process jobs
    this.app.get('/ready', async (req, res) => {
      try {
        // Return unhealthy if server is shutting down
        if (this.isShuttingDown) {
          return res.status(503).json({
            ready: false,
            reason: 'Server is shutting down',
            timestamp: new Date().toISOString(),
          });
        }

        const health = await this.worker.healthCheck();
        const redisConnected = isRedisConnected();

        const isReady = health.status === 'healthy' && redisConnected;

        res.status(isReady ? 200 : 503).json({
          ready: isReady,
          workerId: health.workerId,
          redis: redisConnected,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(503).json({
          ready: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Liveness probe - checks if worker process is alive
    this.app.get('/live', (req, res) => {
      // Liveness probe should remain healthy during shutdown to allow graceful termination
      res.status(200).json({
        alive: true,
        shutting_down: this.isShuttingDown,
        pid: process.pid,
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
      });
    });

    // Worker statistics endpoint
    this.app.get('/stats', async (req, res) => {
      try {
        const stats = await this.worker.getStats();
        res.json(stats);
      } catch (error) {
        res.status(500).json({
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Prometheus metrics endpoint
    this.app.get('/metrics', metricsHandler);
  }

  /**
   * Start the health check server
   */
  async start() {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.port, err => {
        if (err) {
          reject(err);
        } else {
          console.log(`🏥 Worker health check server listening on port ${this.port}`);
          resolve();
        }
      });
    });
  }

  /**
   * Stop the health check server gracefully
   */
  async stop() {
    if (this.server && !this.isShuttingDown) {
      this.isShuttingDown = true;
      console.log('🏥 Starting graceful shutdown of health check server...');

      return new Promise(resolve => {
        // Set a timeout for forceful shutdown if graceful shutdown takes too long
        const shutdownTimeout = setTimeout(() => {
          console.log('🏥 Forcing health check server shutdown due to timeout');
          this.server.close(() => {
            console.log('🏥 Worker health check server forced shutdown completed');
            resolve();
          });
        }, 5000); // 5 second timeout

        // Attempt graceful shutdown
        this.server.close(() => {
          clearTimeout(shutdownTimeout);
          console.log('🏥 Worker health check server stopped gracefully');
          resolve();
        });

        // Stop accepting new connections immediately
        this.server.closeAllConnections?.(); // Available in Node.js 18.02.0+
      });
    }
  }

  /**
   * Initiate shutdown sequence (called when SIGTERM is received)
   */
  initiateShutdown() {
    console.log('🏥 Health check server received shutdown signal');
    this.isShuttingDown = true;
  }
}

module.exports = WorkerHealthServer;
