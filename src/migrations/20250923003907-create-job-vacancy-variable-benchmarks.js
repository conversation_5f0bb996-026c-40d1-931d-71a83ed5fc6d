'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('job_vacancy_variable_benchmarks', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      job_vacancy_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_vacancies',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      job_variable_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_variables',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      baseline_scale: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      baseline_score: {
        type: Sequelize.DOUBLE,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    await queryInterface.addIndex(
      'job_vacancy_variable_benchmarks',
      ['job_vacancy_id', 'job_variable_id'],
      {
        unique: true,
        name: 'job_vacancy_variable_benchmarks_jv_id_jv_id_unique_constraint',
      },
    );
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('job_vacancy_variable_benchmarks');
  },
};
