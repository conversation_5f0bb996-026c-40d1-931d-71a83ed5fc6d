const ApplicationInput = require('./ApplicationInput');

class JobVacancyPipelinesIndexInput extends ApplicationInput {
  /**
   * Define the JSON schema for job vacancy pipelines index validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        job_vacancy_id: {
          type: 'integer',
          minimum: 1,
        },
        sort: {
          type: 'string',
          enum: ['id', 'name', 'parameterized_name', 'order_level', 'created_at', 'updated_at'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
        page: {
          type: 'integer',
          minimum: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
        },
      },
      required: ['job_vacancy_id'],
      additionalProperties: false,
    };
  }
}

module.exports = JobVacancyPipelinesIndexInput;
