const { Sequelize, Model, DataTypes, Utils } = require('sequelize');
const cls = require('cls-hooked');
const config = require('./database.json');

const namespace = cls.createNamespace('paragon-app');
Sequelize.useCLS(namespace);

class LTREE {
  toSql() {
    return 'LTREE';
  }

  toString() {
    return this.toSql();
  }

  _stringify(value) {
    return value;
  }

  static parse(value) {
    return value;
  }

  static get key() {
    return 'LTREE';
  }

  get key() {
    return 'LTREE';
  }
}

DataTypes.LTREE = Utils.classToInvokable(LTREE);
Sequelize.LTREE = Utils.classToInvokable(LTREE);

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

const sequelize = new Sequelize(dbConfig.database, dbConfig.username, dbConfig.password, {
  host: dbConfig.host,
  port: dbConfig.port,
  dialect: dbConfig.dialect,
  logging: dbConfig.logging,
  pool: dbConfig.pool,
  dialectOptions: dbConfig.dialectOptions || {},
});

const transaction = async callback => {
  const transaction = await namespace.get('transaction');
  const options = transaction ? { transaction } : {};
  return await sequelize.transaction(options, callback);
};

module.exports = {
  Sequelize,
  Model,
  DataTypes,
  sequelize,
  dbConfig,
  namespace,
  transaction,
};
