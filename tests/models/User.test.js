const { User } = require('../../src/models');
const SetHierarchyPathJob = require('../../src/jobs/SetHierarchyPathJob');

jest.mock('bullmq');
jest.mock('../../src/config/redis');

describe('User Model', () => {
  const randomizer = () => Math.floor(Math.random() * 1000000);
  const randEmail = () => `user${randomizer()}@example.com`;

  const randUser = () => {
    return {
      name: 'Test User',
      email: randEmail(),
      password: 'password123',
    };
  };

  describe('password hashing', () => {
    it('should hash password before create', async () => {
      const userData = {
        name: 'Test User',
        email: randEmail(),
        password: 'password123',
      };

      const user = await User.create(userData);
      expect(user.password_digest).toBeDefined();
      expect(user.password_digest).not.toBe(userData.password);
    });
  });

  describe('hierarchy_path', () => {
    let cto;
    let principalEngineer;
    let engineeringManager;
    let backendEngineer;
    let frontendEngineer;

    beforeEach(async () => {
      global.performAsyncSpy.mockImplementation(async data => {
        const dataKeys = ['user_id', 'old_path', 'old_supervisor_id', 'new_supervisor_id'];
        const isSetHierarchyPathJob = Object.keys(data).every(key => dataKeys.includes(key));
        if (!isSetHierarchyPathJob) return;

        const job = new SetHierarchyPathJob();
        await job.perform(data);
      });
    });

    it('should update hierarchy_path on create', async () => {
      cto = await User.create(randUser());
      expect(global.performAsyncSpy).toHaveBeenCalledWith({
        user_id: cto.id,
        old_path: null,
        old_supervisor_id: null,
        new_supervisor_id: cto.supervisor_id,
      });

      await cto.reload();
      let expectedPath = cto.id.toString();
      expect(cto.hierarchy_path).toBe(expectedPath);

      engineeringManager = await User.create({ ...randUser(), supervisor_id: cto.id });
      expect(global.performAsyncSpy).toHaveBeenCalledWith({
        user_id: engineeringManager.id,
        old_path: null,
        old_supervisor_id: null,
        new_supervisor_id: cto.id,
      });

      await engineeringManager.reload();
      expectedPath = `${cto.id}.${engineeringManager.id}`;
      expect(engineeringManager.hierarchy_path).toBe(expectedPath);
    });

    it('should update hierarchy_path on update', async () => {
      cto = await User.create(randUser());
      await cto.reload();
      let expectedPath = cto.id.toString();
      expect(cto.hierarchy_path).toBe(expectedPath);

      engineeringManager = await User.create(randUser());
      await engineeringManager.reload();
      expectedPath = engineeringManager.id.toString();
      expect(engineeringManager.hierarchy_path).toBe(expectedPath);

      await engineeringManager.update({ supervisor_id: cto.id });
      expect(global.performAsyncSpy).toHaveBeenCalledWith({
        user_id: engineeringManager.id,
        old_path: expectedPath,
        old_supervisor_id: null,
        new_supervisor_id: cto.id,
      });

      await engineeringManager.reload();
      expectedPath = `${cto.id}.${engineeringManager.id}`;
      expect(engineeringManager.hierarchy_path).toBe(expectedPath);
    });

    it('should handle when we insert data from bottom correctly', async () => {
      // Condition: all user created but without supervisor_id
      [cto, principalEngineer, engineeringManager, backendEngineer, frontendEngineer] =
        await Promise.all(Array.from({ length: 5 }, () => User.create(randUser())));

      await cto.reload();
      expect(cto.hierarchy_path).toBe(cto.id.toString());

      await principalEngineer.reload();
      expect(principalEngineer.hierarchy_path).toBe(principalEngineer.id.toString());

      await engineeringManager.reload();
      expect(engineeringManager.hierarchy_path).toBe(engineeringManager.id.toString());

      await backendEngineer.reload();
      expect(backendEngineer.hierarchy_path).toBe(backendEngineer.id.toString());

      await frontendEngineer.reload();
      expect(frontendEngineer.hierarchy_path).toBe(frontendEngineer.id.toString());

      await backendEngineer.update({ supervisor_id: engineeringManager.id });
      await backendEngineer.reload();
      let expectedPath = `${engineeringManager.id}.${backendEngineer.id}`;
      expect(backendEngineer.hierarchy_path).toBe(expectedPath);

      await frontendEngineer.update({ supervisor_id: engineeringManager.id });
      await frontendEngineer.reload();
      expectedPath = `${engineeringManager.id}.${frontendEngineer.id}`;
      expect(frontendEngineer.hierarchy_path).toBe(expectedPath);

      await principalEngineer.update({ supervisor_id: cto.id });
      await principalEngineer.reload();
      expectedPath = `${cto.id}.${principalEngineer.id}`;
      expect(principalEngineer.hierarchy_path).toBe(expectedPath);

      await engineeringManager.update({ supervisor_id: cto.id });
      await engineeringManager.reload();
      expectedPath = `${cto.id}.${engineeringManager.id}`;
      expect(engineeringManager.hierarchy_path).toBe(expectedPath);

      // Now with EM under CTO, BE and FE should be updated too
      await backendEngineer.reload();
      expectedPath = `${cto.id}.${engineeringManager.id}.${backendEngineer.id}`;
      expect(backendEngineer.hierarchy_path).toBe(expectedPath);

      await frontendEngineer.reload();
      expectedPath = `${cto.id}.${engineeringManager.id}.${frontendEngineer.id}`;
      expect(frontendEngineer.hierarchy_path).toBe(expectedPath);
    });

    it('should handle when we update middle guy correctly', async () => {
      let returnData;
      global.performAsyncSpy.mockImplementation(async data => {
        const job = new SetHierarchyPathJob();
        returnData = await job.perform(data);
      });

      // Condition: all user created with supervisor_id
      cto = await User.create(randUser());
      await cto.reload();
      let expectedPath = cto.id.toString();
      expect(cto.hierarchy_path).toBe(expectedPath);
      expect(returnData.updated_count).toBe('1');

      principalEngineer = await User.create({ ...randUser(), supervisor_id: cto.id });
      await principalEngineer.reload();
      expectedPath = `${cto.id}.${principalEngineer.id}`;
      expect(principalEngineer.hierarchy_path).toBe(expectedPath);
      expect(returnData.updated_count).toBe('1');

      engineeringManager = await User.create({ ...randUser(), supervisor_id: cto.id });
      await engineeringManager.reload();
      expectedPath = `${cto.id}.${engineeringManager.id}`;
      expect(engineeringManager.hierarchy_path).toBe(expectedPath);
      expect(returnData.updated_count).toBe('1');

      backendEngineer = await User.create({ ...randUser(), supervisor_id: engineeringManager.id });
      frontendEngineer = await User.create({ ...randUser(), supervisor_id: engineeringManager.id });

      await backendEngineer.reload();
      expectedPath = `${cto.id}.${engineeringManager.id}.${backendEngineer.id}`;
      expect(backendEngineer.hierarchy_path).toBe(expectedPath);

      await frontendEngineer.reload();
      expectedPath = `${cto.id}.${engineeringManager.id}.${frontendEngineer.id}`;
      expect(frontendEngineer.hierarchy_path).toBe(expectedPath);

      // Now EM reports to PE, BE and FE should be updated too to have deeper path
      await engineeringManager.update({ supervisor_id: principalEngineer.id });
      await engineeringManager.reload();
      expectedPath = `${cto.id}.${principalEngineer.id}.${engineeringManager.id}`;
      expect(engineeringManager.hierarchy_path).toBe(expectedPath);

      // So, because EM has two subordinates, we expect the return data to have 3 updated users
      expect(returnData.updated_count).toBe('3');

      const prevBePathLevels = backendEngineer.hierarchy_path.split('.').length;
      expect(prevBePathLevels).toBe(3);

      await backendEngineer.reload();
      expectedPath = `${cto.id}.${principalEngineer.id}.${engineeringManager.id}.${backendEngineer.id}`;
      expect(backendEngineer.hierarchy_path).toBe(expectedPath);
      expect(backendEngineer.hierarchy_path.split('.').length).toBe(prevBePathLevels + 1);

      const prevFePathLevels = frontendEngineer.hierarchy_path.split('.').length;
      expect(prevFePathLevels).toBe(3);

      await frontendEngineer.reload();
      expectedPath = `${cto.id}.${principalEngineer.id}.${engineeringManager.id}.${frontendEngineer.id}`;
      expect(frontendEngineer.hierarchy_path).toBe(expectedPath);
      expect(frontendEngineer.hierarchy_path.split('.').length).toBe(prevFePathLevels + 1);

      // Expected cto and pe hierarchy_path to be unchanged
      await cto.reload();
      expectedPath = cto.id.toString();
      expect(cto.hierarchy_path).toBe(expectedPath);

      await principalEngineer.reload();
      expectedPath = `${cto.id}.${principalEngineer.id}`;
      expect(principalEngineer.hierarchy_path).toBe(expectedPath);
    });

    it('should handle concurrent updates correctly', async () => {
      // Condition: all user created without supervisor_id
      [cto, principalEngineer, engineeringManager, backendEngineer, frontendEngineer] =
        await Promise.all(Array.from({ length: 5 }, () => User.create(randUser())));

      await cto.reload();
      await principalEngineer.reload();
      await engineeringManager.reload();
      await backendEngineer.reload();
      await frontendEngineer.reload();

      // Concurrently update supervisor_id for all users
      const supervisorData = {
        [principalEngineer.id]: cto.id,
        [engineeringManager.id]: cto.id,
        [backendEngineer.id]: engineeringManager.id,
        [frontendEngineer.id]: engineeringManager.id,
      };

      await Promise.all(
        Object.entries(supervisorData).map(async ([userId, supervisorId]) => {
          const job = new SetHierarchyPathJob();
          await job.perform({
            user_id: userId,
            old_path: null,
            old_supervisor_id: null,
            new_supervisor_id: supervisorId,
          });
        }),
      );

      await cto.reload();
      expect(cto.hierarchy_path).toBe(cto.id.toString());

      await principalEngineer.reload();
      expect(principalEngineer.hierarchy_path).toBe(`${cto.id}.${principalEngineer.id}`);

      await engineeringManager.reload();
      expect(engineeringManager.hierarchy_path).toBe(`${cto.id}.${engineeringManager.id}`);

      await backendEngineer.reload();
      expect(backendEngineer.hierarchy_path).toBe(
        `${cto.id}.${engineeringManager.id}.${backendEngineer.id}`,
      );

      await frontendEngineer.reload();
      expect(frontendEngineer.hierarchy_path).toBe(
        `${cto.id}.${engineeringManager.id}.${frontendEngineer.id}`,
      );
    });

    it('should throw error for circular dependency', async () => {
      cto = await User.create(randUser());
      await cto.reload();
      let expectedPath = cto.id.toString();
      expect(cto.hierarchy_path).toBe(expectedPath);

      engineeringManager = await User.create({ ...randUser(), supervisor_id: cto.id });
      await engineeringManager.reload();
      expectedPath = `${cto.id}.${engineeringManager.id}`;
      expect(engineeringManager.hierarchy_path).toBe(expectedPath);

      await cto.reload();
      await engineeringManager.reload();
      await expect(cto.update({ supervisor_id: engineeringManager.id })).rejects.toThrow(
        'Circular dependency detected',
      );
    });

    it('should throw error for supervisor not found', async () => {
      cto = await User.create(randUser());
      await cto.reload();
      const expectedPath = cto.id.toString();
      expect(cto.hierarchy_path).toBe(expectedPath);

      await expect(cto.update({ supervisor_id: -1 })).rejects.toThrow(
        'Supervisor with ID -1 not found',
      );
    });

    it('should throw error for user not found', async () => {
      cto = await User.create(randUser());
      await cto.reload();
      const expectedPath = cto.id.toString();
      expect(cto.hierarchy_path).toBe(expectedPath);

      principalEngineer = await User.create(randUser());
      await principalEngineer.reload();

      await principalEngineer.destroy();
      await expect(principalEngineer.update({ supervisor_id: cto.id })).rejects.toThrow(
        `User with ID ${principalEngineer.id} not found`,
      );
    });
  });
});
