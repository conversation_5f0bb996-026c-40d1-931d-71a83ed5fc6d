const ApplicationInput = require('./ApplicationInput');

class UserJobVacanciesIndexInput extends ApplicationInput {
  /**
   * Define the JSON schema for user job vacancies index validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        job_vacancy_id: {
          type: 'integer',
          minimum: 1,
        },
        status: {
          type: 'string',
          minLength: 1,
          maxLength: 255,
        },
        sort: {
          type: 'string',
          enum: ['id', 'match_rate', 'status', 'created_at', 'updated_at'],
        },
        sort_direction: {
          type: 'string',
          enum: ['asc', 'desc'],
        },
        page: {
          type: 'integer',
          minimum: 1,
        },
        limit: {
          type: 'integer',
          minimum: 1,
          maximum: 100,
        },
        search: {
          type: 'string',
        },
      },
      required: [],
      additionalProperties: false,
    };
  }
}

module.exports = UserJobVacanciesIndexInput;
