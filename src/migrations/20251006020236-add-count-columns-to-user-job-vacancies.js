'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('user_job_vacancies', 'incomplete_variables', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    });

    await queryInterface.addColumn('user_job_vacancies', 'filtered_out_variable_scales', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('user_job_vacancies', 'incomplete_variables');
    await queryInterface.removeColumn('user_job_vacancies', 'filtered_out_variable_scales');
  },
};
