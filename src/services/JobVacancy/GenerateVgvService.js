const AppService = require('../AppService');
const { Bone, JobVacancy, JobGroupVariable } = require('../../models');
const LogLlmInteractionJob = require('../../jobs/LogLlmInteractionJob');

// --- Constants ---
const ONET_DATA_KEYS = [
  'knowledges',
  'skills',
  'abilities',
  'interests',
  'work_values',
  'work_styles',
];

const BONE_MAPPINGS = {
  CCPS: { Business: 7, Functional: 5, Professional: 6 },
  SOC: { Business: 6, Functional: 4, Professional: 3 },
  CIO: { Business: 5, Functional: 4, Professional: 7 },
  CR: { Business: 6, Functional: 7, Professional: 5 },
  AST: { Business: 7, Functional: 5, Professional: 5 },
  MD: { Business: 7, Functional: 5, Professional: 6 },
  LI: { Business: 7, Functional: 5, Professional: 4 },
  CVF: { Business: 6, Functional: 5, Professional: 5 },
};

const VGV_CONFIG = {
  sectionWeights: {
    KR: 0.4,
    SC: 0.4,
    Q: 0.2,
  },
  sectionMappings: {
    KR: 'key_responsibilities',
    SC: 'competencies',
    Q: 'qualifications',
  },
  strengthWeights: {
    strong: 1.0,
    medium: 0.6,
    light: 0.3,
  },
  smoothing: {
    kappa: 0.8, // Smoothing factor
  },
};

const KSAO_SYSTEM_PROMPT = `
## Role and Context
You are an expert HR professional specializing in job analysis and competency modeling. Your expertise includes translating job requirements into structured Knowledge, Skills, Abilities, and Other characteristics (KSAO) frameworks that align with industry standards and O*NET occupational data.

## Task Overview
Create a comprehensive KSAO profile for a specific job vacancy by analyzing job descriptions and leveraging O*NET occupational data as supporting reference material.

## Input Structure
You will receive:
1. **Job Title**: The specific position title
2. **Job Descriptions**: Detailed list of key job responsibilities, qualifications, skill and competencies, and success metrics
3. **Related O*NET Data**: Occupational information including:
  - Knowledge areas
  - Skills requirements
  - Abilities needed
  - Work interests
  - Work values
  - Work styles

## Instructions

### Step 1: Analysis
- Carefully analyze the provided job descriptions to identify core responsibilities and requirements
- Cross-reference with O*NET data to identify relevant competencies
- Prioritize job-specific requirements while using O*NET data to fill gaps and validate decisions

### Step 2: KSAO Development
Create four distinct categories using these definitions and formatting guidelines:

**Knowledge (K)**: Body of facts and information someone must know
- Write as short, specific topic areas or subject matter domains
- Use concise phrases (3-8 words typically)
- Focus on what someone needs to know, not how they apply it
- Examples: "Brand positioning and messaging", "Employment law and regulations", "Financial analysis principles"

**Skills (S)**: Learned proficiencies that can be demonstrated
- Write as actionable capabilities or techniques
- Use verb phrases when appropriate
- Focus on what someone can do or perform
- Examples: "Develop brand strategy", "Conduct market research", "Data analysis and reporting"

**Abilities (A)**: Enduring attributes that support performance
- Write as inherent capacities or traits
- Often start with "Ability to..." but keep concise
- Focus on cognitive, physical, or interpersonal capabilities
- Examples: "Strategic thinking", "Ability to influence stakeholders", "Analytical reasoning"

**Other Characteristics (O)**: Traits, motivations, values, or work styles
- Write as personal attributes or orientations
- Focus on personality traits, work preferences, and motivational factors
- Examples: "Creative mindset", "Detail orientation", "Growth mindset"

### Step 3: Quality Standards
- Keep each item concise (typically 2-6 words, maximum 8 words)
- Ensure items are specific and job-relevant
- Avoid lengthy explanations or examples in the KSAO items themselves
- Maintain clear distinction between categories
- Aim for 6-10 items per category for comprehensive coverage
- Prioritize the most critical competencies for job success

## Output Format
Return your response as a valid JSON object with the following structure:
{
  "knowledges": ["string"],
  "skills": ["string"],
  "abilities": ["string"],
  "other_characteristics": ["string"]
}

## Important Notes
- Base your KSAO primarily on the job descriptions provided
- Use O*NET data as supporting reference to enhance and validate your analysis
- Keep all items concise and specific - avoid lengthy explanations
- Focus on the most essential competencies for job success
- Ensure all items are directly relevant to the specific job vacancy
- Maintain professional HR terminology and standards
- Double-check that your output is valid JSON format
`;

const VGV_SYSTEM_PROMPT = `
You are an expert annotator for talent analytics. Your task is to map phrases from a Job Description (JD) to exactly one of 8 Group Variables (GVs).

Output must be in JSON format only, strictly following the provided schema. Use direct quotes from the JD as evidence; do not invent or paraphrase text.

**Group Variables (GVs) - Choose one per phrase:**
// 1. **Conscientiousness & Reliability (CR):** Accuracy, detail-orientation, documentation, compliance, record-keeping, time management, orderliness.
// 2. **Social Orientation & Collaboration (SOC):** Coordination, communication, teamwork, stakeholder interaction, customer service.
// 3. **Cognitive Complexity & Problem-Solving (CCPS):** Analysis, reasoning, troubleshooting, evaluation, data-driven decision-making.
// 4. **Adaptability & Stress Tolerance (AST):** Working under pressure, managing deadlines, flexibility, change readiness, handling ambiguity.
// 5. **Leadership & Influence (LI):** Leading teams, guiding, influencing, facilitating, mentoring.
// 6. **Motivation & Drive (MD):** Initiative, proactivity, target orientation, achievement-driven, ownership.
// 7. **Creativity & Innovation Orientation (CIO):** Ideation, design, process improvement, experimentation.
// 8. **Cultural & Values Fit (CVF):** Adherence to ethics, policy, regulations, labor law, company values.
{{JGV_PHRASES}}

**Strength Scale:**
- "strong": An exact or near-exact competency term is present (e.g., "attention to detail," "compliance").
- "medium": A clear synonym or descriptive phrase is used (e.g., "maintain accurate records").
- "light": A weak or general cue is identified (e.g., "generates reports"), used only if clearly relevant.

**Confidence:**
- A score from 0.00 to 1.00. If uncertain, select the most plausible GV and assign a lower confidence score.
`;

const VGV_USER_PROMPT = `
JOB_TITLE: {{jobTitle}}
SECTION_WEIGHTS: {{sectionWeights}}

JD_SECTIONS: {{jdSections}}

Return JSON with this schema:
{
  "job_title": string,
  "phrases": [
    {
      "section": "KR"|"SC"|"Q",
      "phrase": string,          // short evidence as appears in JD
      "gv": "CR"|"SOC"|"CCPS"|"AST"|"LI"|"MD"|"CIO"|"CVF",
      "strength": "strong"|"medium"|"light",
      "confidence": number       // 0.0-1.0
    }, ...
  ]
}
Return only JSON. Do not include any extra text.
`;

class GenerateVgvService extends AppService {
  constructor({ googleAiService, onetService }) {
    super();
    this.googleAiService = googleAiService;
    this.onetService = onetService;
  }

  async generateKsao(vacancy) {
    const {
      name: vacancyName,
      detailed_descriptions: jobDetails = {},
      job_desc: fallbackJobDesc,
      related_onetsoc_codes: onetsocCodes,
    } = vacancy;

    const jobDesc = jobDetails.key_responsibilities || fallbackJobDesc;
    const occupations = await this.getOccupationsData(onetsocCodes);
    const userPrompt = this.buildKsaoUserPrompt(vacancyName, occupations, jobDetails, jobDesc);

    const aiParams = this.buildAiParams(userPrompt, KSAO_SYSTEM_PROMPT);
    const response = await this.googleAiService.generateContent(aiParams);

    await LogLlmInteractionJob.perform_async({
      request: aiParams,
      responses: response,
      actionType: 'generate_ksao',
    });

    return this.parseAiResponse(response);
  }

  async generateVgvRecords({ ksao, jobVacancyId, boneId, jgvAvgScales }) {
    const jgvIds = Object.keys(jgvAvgScales);
    const jgvPromise = JobGroupVariable.findAll({
      where: { id: jgvIds },
      attributes: ['id', 'name', 'jgv_code', 'phrases', 'keywords'],
      order: [['id', 'ASC']],
    });

    const flattenedKsao = Object.values(ksao)
      .flat()
      .map(item => String(item).toLowerCase())
      .join(';');

    const bonePromise = boneId ? Bone.findByPk(boneId) : null;
    const vacancyPromise = JobVacancy.findByPk(jobVacancyId);

    const [jobGroupVariables, bone, vacancy] = await Promise.all([
      jgvPromise,
      bonePromise,
      vacancyPromise,
    ]);

    let weights = {};
    const weightsCalculation = this.config.vacancyGroupVariable.defaultWeightsCalculation;
    const boneName = bone?.name || null;
    const nonZeroJgvCount = Object.values(jgvAvgScales).filter(scale => scale !== 0).length;

    if (weightsCalculation === 'dirichlet_phrases') {
      const phrases = await this.generatePhrases(jobGroupVariables, vacancy);
      weights = this.calculateVgvWeights(phrases, jobGroupVariables);
    }

    return jobGroupVariables.map(jgv => {
      const matchCount = jgv.keywords.reduce(
        (count, keyword) => (flattenedKsao.includes(keyword) ? count + 1 : count),
        0,
      );

      let weight;
      const boneMapping = BONE_MAPPINGS[jgv.jgv_code] || {};
      const boneValue = boneName ? boneMapping[boneName] || 0 : 0;

      if (weightsCalculation === 'dirichlet_phrases') {
        weight = weights[jgv.jgv_code] || 0;
      } else {
        weight = 1 / nonZeroJgvCount;
      }

      return {
        job_vacancy_id: jobVacancyId,
        job_group_variable_id: jgv.id,
        keyword_match_count: matchCount,
        keyword_total_count: jgv.keywords.length,
        match_type: 'weight',
        weight,
        default_weight: weight,
        bone_value: boneValue,
        avg_baseline_scale: jgvAvgScales[jgv.id],
        configured_baseline_scale: jgvAvgScales[jgv.id],
        updated_at: new Date(),
        deleted_at: null,
      };
    });
  }

  async getOccupationsData(onetsocCodes) {
    const getMethodName = key => {
      const snakeCaseName = `get_${key}`;
      const camelizedName = snakeCaseName.replace(/_([a-z])/g, g => g[1].toUpperCase());

      return camelizedName;
    };

    const dataPromises = ['occupations', ...ONET_DATA_KEYS].map(key => {
      const methodName = getMethodName(key);
      const dataPromise = this.onetService[methodName](onetsocCodes);

      return dataPromise;
    });

    const [occupations, ...referenceDataSets] = await Promise.all(dataPromises);
    const referenceData = ONET_DATA_KEYS.reduce((acc, key, index) => {
      acc[key] = referenceDataSets[index];
      return acc;
    }, {});

    for (const [key, items] of Object.entries(referenceData)) {
      for (const item of items) {
        const { onetsoc_code, ...rest } = item;
        if (occupations[onetsoc_code]) {
          occupations[onetsoc_code][key] = occupations[onetsoc_code][key] || [];
          occupations[onetsoc_code][key].push(rest);
        }
      }
    }

    return occupations;
  }

  async generatePhrases(jobGroupVariables, vacancy) {
    const jobTitle = vacancy.name;
    const jobDetails = vacancy.detailed_descriptions;

    const aiConfig = { temperature: 0 };
    const systemPrompt = this.buildVgvSystemPrompt(jobGroupVariables);
    const userPrompt = this.buildVgvUserPrompt(jobTitle, jobDetails);
    const aiParams = this.buildAiParams(userPrompt, systemPrompt, aiConfig);
    const response = await this.googleAiService.generateContent(aiParams);

    await LogLlmInteractionJob.perform_async({
      request: aiParams,
      responses: response,
      actionType: 'generate_phrases',
    });

    const parsedAiResponse = await this.parseAiResponse(response);
    return parsedAiResponse?.phrases || [];
  }

  buildKsaoUserPrompt(vacancyName, occupations, jobDetails, jobDesc) {
    let userPrompt = `# Job Title\n${vacancyName}\n\n`;

    const mapJobDetails = array => {
      return array.map(item => `- ${item}\n\n`).join('');
    };

    if (jobDetails) {
      userPrompt += `# Job Details\n`;
      userPrompt += '## Job Key Responsibilities\n';
      userPrompt += mapJobDetails(jobDetails.key_responsibilities);
      userPrompt += '## Job Qualifications\n';
      userPrompt += mapJobDetails(jobDetails.qualifications);
      userPrompt += '## Job Skill & Competencies\n';
      userPrompt += mapJobDetails(jobDetails.competencies);
      userPrompt += '## Job Success Metrics\n';
      userPrompt += mapJobDetails(jobDetails.success_metrics);
    } else if (jobDesc) {
      userPrompt += `# Job Descriptions\n`;
      userPrompt += mapJobDetails(jobDesc);
    }

    userPrompt += '\n# Related O*NET Data\n';
    Object.values(occupations).forEach(occupation => {
      userPrompt += `## Occupation: ${occupation.title}\n`;
      userPrompt += `### Description\n${occupation.description}\n\n`;

      ONET_DATA_KEYS.forEach(key => {
        if (occupation[key] && occupation[key].length > 0) {
          userPrompt += `### ${key.replace('_', ' ')}\n`;
          userPrompt += `\`\`\`json\n`;
          userPrompt += JSON.stringify(occupation[key], null, 2);
          userPrompt += `\n\`\`\`\n\n`;
        }
      });
    });

    return userPrompt;
  }

  buildVgvSystemPrompt(jobGroupVariables) {
    let jgvPhrases = '';
    jobGroupVariables.forEach((jgv, index) => {
      jgvPhrases += `${index + 1}. **${jgv.name} (${jgv.jgv_code}):** ${jgv.phrases}\n`;
    });

    return VGV_SYSTEM_PROMPT.replace('{{JGV_PHRASES}}', jgvPhrases);
  }

  buildVgvUserPrompt(vacancyName, jobDetails) {
    const sections = Object.entries(VGV_CONFIG.sectionMappings).map(([section, key]) => {
      return {
        section,
        text: Array.isArray(jobDetails[key]) ? jobDetails[key].join(' ') : jobDetails[key],
      };
    });

    const userPrompt = VGV_USER_PROMPT.replace('{{jobTitle}}', vacancyName)
      .replace('{{sectionWeights}}', JSON.stringify(VGV_CONFIG.sectionWeights, null, 2))
      .replace('{{jdSections}}', JSON.stringify(sections, null, 2));

    return userPrompt;
  }

  calculateVgvWeights(phrases, jobGroupVariables) {
    // Step 1: Calculate individual phrase scores
    const scoredPhrases = phrases.map(phrase => {
      const sectionWeight = VGV_CONFIG.sectionWeights[phrase.section] || 0;
      const strengthWeight = VGV_CONFIG.strengthWeights[phrase.strength] || 0;
      const confidence = phrase.confidence || 0;

      const score = sectionWeight * strengthWeight * confidence;

      return { ...phrase, score };
    });

    // Step 2: Aggregate scores by Group Variable (GV)
    const aggregatedScores = {};
    jobGroupVariables.forEach(jgv => (aggregatedScores[jgv.jgv_code] = 0));

    scoredPhrases.forEach(phrase => {
      if (Object.prototype.hasOwnProperty.call(aggregatedScores, phrase.gv)) {
        aggregatedScores[phrase.gv] += phrase.score;
      }
    });

    // Step 3: Apply Dirichlet Smoothing
    const finalWeights = this.applyDirichletSmoothing(
      aggregatedScores,
      jobGroupVariables,
      VGV_CONFIG.smoothing.kappa,
    );

    return finalWeights;
  }

  applyDirichletSmoothing(scores, jobGroupVariables, kappa) {
    const numGVs = jobGroupVariables.length;
    const pi_g = 1 / numGVs;
    const smoothingValue = kappa * pi_g;

    const totalRawScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
    const denominator = totalRawScore + kappa;

    const finalWeights = {};
    for (const jgv of jobGroupVariables) {
      const jgvCode = jgv.jgv_code;
      const Sg = scores[jgvCode] || 0;
      const numerator = Sg + smoothingValue;
      finalWeights[jgvCode] = numerator / denominator;
    }

    return finalWeights;
  }

  buildAiParams(userPrompt, systemPrompt, config = {}) {
    const modelName = config.modelName || 'gemini-2.5-flash';
    delete config.modelName;

    return {
      model: modelName,
      contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
      config: {
        temperature: 0.2,
        responseMimeType: 'application/json',
        thinkingConfig: { thinkingBudget: -1 },
        systemInstruction: [{ text: systemPrompt }],
        ...config,
      },
    };
  }

  parseAiResponse(response) {
    try {
      const content = response?.candidates?.[0]?.content?.parts?.[0]?.text;
      if (content) {
        return JSON.parse(content);
      }

      throw new Error('Invalid AI response structure. No content found.');
    } catch (error) {
      console.error(
        'Failed to parse AI response:',
        error,
        'Raw content:',
        response?.candidates?.[0]?.content?.parts?.[0]?.text,
      );

      throw new Error('Could not parse data from AI response.');
    }
  }
}

module.exports = GenerateVgvService;
