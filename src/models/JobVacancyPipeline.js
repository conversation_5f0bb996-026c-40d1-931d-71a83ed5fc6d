'use strict';
const { DataTypes } = require('../config/sequelize');
const AppModel = require('./AppModel');

class JobVacancyPipeline extends AppModel {
  static associate(models) {
    this.belongsTo(models.JobVacancy, {
      foreignKey: 'job_vacancy_id',
      as: 'jobVacancy',
    });

    this.hasMany(models.UserJobVacancyPipeline, {
      foreignKey: 'job_vacancy_pipeline_id',
      as: 'userJobVacancyPipelines',
    });
  }

  static schema() {
    return {
      parameterized_name: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [1, 255],
          is: /^[a-z0-9_]+$/i, // Only alphanumeric and underscores
        },
      },
    };
  }

  static options() {
    return {
      tableName: 'job_vacancy_pipelines',
      indexes: [
        {
          fields: ['job_vacancy_id'],
        },
        {
          fields: ['order_level'],
        },
        {
          fields: ['parameterized_name', 'job_vacancy_id'],
          unique: true,
        },
      ],
    };
  }
}

module.exports = JobVacancyPipeline;
