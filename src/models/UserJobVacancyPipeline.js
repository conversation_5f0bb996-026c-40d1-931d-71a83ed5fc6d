'use strict';
const AppModel = require('./AppModel');

class UserJobVacancyPipeline extends AppModel {
  static associate(models) {
    this.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });

    this.belongsTo(models.JobVacancyPipeline, {
      foreignKey: 'job_vacancy_pipeline_id',
      as: 'jobVacancyPipeline',
    });

    this.belongsTo(models.User, {
      foreignKey: 'created_by_id',
      as: 'createdBy',
    });

    this.belongsTo(models.User, {
      foreignKey: 'approved_by_id',
      as: 'approvedBy',
    });
  }

  static options() {
    return {
      tableName: 'user_job_vacancy_pipelines',
      indexes: [
        {
          fields: ['user_id'],
        },
        {
          fields: ['job_vacancy_pipeline_id'],
        },
        {
          fields: ['created_by_id'],
        },
        {
          fields: ['approved_by_id'],
        },
        {
          fields: ['user_id', 'job_vacancy_pipeline_id'],
          unique: true,
        },
      ],
    };
  }
}

module.exports = UserJobVacancyPipeline;
