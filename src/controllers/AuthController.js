const ApiController = require('./ApiController');
const AuthService = require('../services/AuthService');
const AuthLoginInput = require('../inputs/AuthLoginInput');
const AuthOutput = require('../outputs/AuthOutput');

class AuthController extends ApiController {
  constructor() {
    super();
    this.service = new AuthService();
  }

  /**
   * Inspect user role and permissions
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const output = new AuthOutput(req.authToken, {
      user: req.user,
      permissions: req.permissions,
    });
    output.renderJson(res);
  });

  /**
   * Handle user login request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  login = this.createMethod(async (req, res) => {
    const input = new AuthLoginInput(req.body);
    input.validate();

    const result = await this.service.login(input.output());
    const output = new AuthOutput(result.authToken, {
      user: result.user,
      permissions: result.permissions,
    });

    output.renderJson(res);
  });
}

module.exports = new AuthController();
