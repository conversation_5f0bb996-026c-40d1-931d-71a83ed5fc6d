const api = require('../utils/requestHelper');

const { UserFactory } = require('../factories');

describe('AuthController', () => {
  describe('POST /api/v1/auth/login', () => {
    let testUser;
    let loginData;

    beforeEach(async () => {
      testUser = await UserFactory.create();

      loginData = {
        email: testUser.email,
        password: 'password123',
      };
    });

    describe('successful login', () => {
      it('should return 200 and auth token for valid credentials', async () => {
        const response = await api.post('/api/v1/auth/login', loginData);

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body.data).toHaveProperty('auth_token');
        expect(response.body.data).toHaveProperty('user');
        expect(response.body.data.user).toMatchObject({
          id: testUser.id,
          name: testUser.name,
          email: testUser.email,
          role: 'user',
        });
        expect(typeof response.body.data.auth_token).toBe('string');
        expect(response.body.data.auth_token.length).toBeGreaterThan(0);

        expect(response.body.data).toHaveProperty('permissions');
        expect(Array.isArray(response.body.data.permissions)).toBe(true);
        expect(response.body.data.permissions.length).toBe(0);
      });

      it('should return 200 and admin permissions for admin', async () => {
        testUser = await UserFactory.create({ role: 'admin', password: 'admin123' });
        const testData = { email: testUser.email, password: 'admin123' };
        const response = await api.post('/api/v1/auth/login', testData);

        expect(response.status).toBe(200);
        expect(response.body.data.permissions.length).toBeGreaterThan(0);
      });

      it('should handle email case normalization', async () => {
        loginData.email = testUser.email.toUpperCase();
        const response = await api.post('/api/v1/auth/login', loginData);

        expect(response.status).toBe(200);
        expect(response.body.data.user.email).toBe(testUser.email);
      });
    });

    describe('validation errors', () => {
      it('should return 400 for missing email', async () => {
        delete loginData.email;
        const response = await api.post('/api/v1/auth/login', loginData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body).toHaveProperty('details');
        expect(Array.isArray(response.body.details)).toBe(true);
      });

      it('should return 400 for missing password', async () => {
        delete loginData.password;
        const response = await api.post('/api/v1/auth/login', loginData);

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body).toHaveProperty('details');
        expect(Array.isArray(response.body.details)).toBe(true);
      });

      it('should return 400 for invalid email format', async () => {
        loginData.email = 'invalid-email';
        const response = await api.post('/api/v1/auth/login', loginData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body).toHaveProperty('details');
        expect(Array.isArray(response.body.details)).toBe(true);
      });
    });

    describe('authentication errors', () => {
      it('should return 401 for non-existent user', async () => {
        loginData.email = '<EMAIL>';
        const response = await api.post('/api/v1/auth/login', loginData);

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Invalid email or password');
      });

      it('should return 401 for invalid password', async () => {
        loginData.password = 'wrongpassword';
        const response = await api.post('/api/v1/auth/login', loginData);

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Invalid email or password');
      });
    });

    describe('edge cases', () => {
      it('should handle empty request body', async () => {
        const response = await api.post('/api/v1/auth/login');

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
        expect(response.body).toHaveProperty('details');
        expect(Array.isArray(response.body.details)).toBe(true);
        expect(response.body.details.length).toBeGreaterThan(0);
      });

      it('should handle malformed JSON', async () => {
        const invalidData = '{"email": "<EMAIL>", "password":}';
        const response = await api.post('/api/v1/auth/login', invalidData);
        expect(response.status).toBe(400);
      });
    });
  });

  describe('GET /api/v1/auth', () => {
    it('should return 200 and user data for valid token', async () => {
      const testUser = await UserFactory.create();
      const response = await api.as(testUser).get('/api/v1/auth');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user).toHaveProperty('id', testUser.id);
      expect(response.body.data).toHaveProperty('permissions');
      expect(Array.isArray(response.body.data.permissions)).toBe(true);
      expect(response.body.data.permissions.length).toBe(0);
    });

    it('should return 200 and admin permissions for admin', async () => {
      const testUser = await UserFactory.create({ role: 'admin' });
      const response = await api.as(testUser).get('/api/v1/auth');

      expect(response.status).toBe(200);
      expect(response.body.data.permissions.length).toBeGreaterThan(0);
    });

    it('should return 401 for blank token', async () => {
      const response = await api.get('/api/v1/auth');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error', 'Access token required');
    });
  });
});
