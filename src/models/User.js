'use strict';
const { DataTypes } = require('../config/sequelize');
const AppModel = require('./AppModel');
const config = require('../config/config');
const bcrypt = require('bcrypt');

class User extends AppModel {
  static associate(models) {
    this.hasOne(models.UserProfile, { foreignKey: 'user_id', as: 'profile' });
    this.hasMany(models.UserPosition, { foreignKey: 'user_id', as: 'positions' });
    this.hasMany(models.UserJobVacancy, { foreignKey: 'user_id', as: 'jobVacancyRecommendations' });

    this.hasMany(models.UserJobVariable, {
      foreignKey: 'user_id',
      as: 'userJobVariables',
    });

    this.hasMany(models.UserVacancyGroupVariable, {
      foreignKey: 'user_id',
      as: 'userVacancyGroupVariables',
    });

    this.hasMany(models.UserCompetenciesProfiling, {
      foreignKey: 'user_id',
      as: 'userCompetenciesProfiling',
    });

    this.hasMany(models.UserAssessmentResult, {
      foreignKey: 'user_id',
      as: 'userAssessmentResults',
    });

    this.hasMany(models.UserBone, {
      foreignKey: 'user_id',
      as: 'userBones',
    });

    this.hasMany(models.UserJobVacancyPipeline, {
      foreignKey: 'user_id',
      as: 'userJobVacancyPipelines',
    });

    this.hasMany(models.UserJobVacancyPipeline, {
      foreignKey: 'created_by_id',
      as: 'createdUserJobVacancyPipelines',
    });

    this.hasMany(models.UserJobVacancyPipeline, {
      foreignKey: 'approved_by_id',
      as: 'approvedUserJobVacancyPipelines',
    });
  }

  static schema() {
    return {
      password: {
        type: DataTypes.VIRTUAL,
        allowNull: false,
        validate: {
          notEmpty: {
            msg: 'Password cannot be empty',
          },
        },
      },
    };
  }

  static hooks() {
    return {
      beforeValidate: async user => {
        if (user.password) {
          const hashedPassword = await bcrypt.hash(user.password, config.saltRounds);
          user.password_digest = hashedPassword;
        }
      },
      afterCreate: async user => {
        const SetHierarchyPathJob = require('../jobs/SetHierarchyPathJob');
        await SetHierarchyPathJob.perform_async({
          user_id: user.id,
          old_path: null,
          old_supervisor_id: null,
          new_supervisor_id: user.supervisor_id,
        });
      },
      afterUpdate: async user => {
        if (!user.changed('supervisor_id')) return;

        const SetHierarchyPathJob = require('../jobs/SetHierarchyPathJob');
        await SetHierarchyPathJob.perform_async({
          user_id: user.id,
          old_path: user.previous('hierarchy_path'),
          old_supervisor_id: user.previous('supervisor_id'),
          new_supervisor_id: user.supervisor_id,
        });
      },
    };
  }

  async verifyPassword(password) {
    return await bcrypt.compare(password, this.password_digest);
  }
}

module.exports = User;
