'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('job_vacancy_pipelines', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      parameterized_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      job_vacancy_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_vacancies',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      order_level: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: null,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    // Add indexes
    await queryInterface.addIndex('job_vacancy_pipelines', ['job_vacancy_id'], {
      name: 'job_vacancy_pipelines_job_vacancy_id_index',
    });

    await queryInterface.addIndex('job_vacancy_pipelines', ['order_level'], {
      name: 'job_vacancy_pipelines_order_level_index',
    });

    // Add unique constraint for parameterized_name and job_vacancy_id
    await queryInterface.addIndex(
      'job_vacancy_pipelines',
      ['parameterized_name', 'job_vacancy_id'],
      {
        unique: true,
        name: 'job_vacancy_pipelines_parameterized_name_job_vacancy_id_unique',
      },
    );
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('job_vacancy_pipelines');
  },
};
