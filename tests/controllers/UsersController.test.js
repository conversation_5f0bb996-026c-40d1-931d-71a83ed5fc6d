const api = require('../utils/requestHelper');

const {
  UserFactory,
  UserProfileFactory,
  UserAssessmentResultFactory,
  UserPositionFactory,
  UserPerformanceReviewFactory,
  UserCompetenciesProfilingFactory,
} = require('../factories');
const { User } = require('../../src/models');

describe('UsersController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();
  });

  describe('GET /api/v1/users', () => {
    beforeEach(async () => {
      await UserFactory.createMany(2);
      await UserFactory.createMany(2, { role: 'admin' });
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/users');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/users');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });

      it('should return 200 for admin users', async () => {
        const response = await api.as(admin).get('/api/v1/users');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(Array.isArray(response.body.data)).toBe(true);
      });
    });

    describe('basic functionality', () => {
      it('should return all users with pagination', async () => {
        const response = await api.as(admin).get('/api/v1/users');

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6); // 2 initial + 4 created in beforeEach
        expect(response.body).toHaveProperty('pagination');
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 10,
          total: 6,
          totalPages: 1,
        });
      });

      it('should handle pagination parameters', async () => {
        const params = { page: 1, limit: 3 };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 3,
          total: 6,
          totalPages: 2,
        });
      });
    });

    describe('filtering', () => {
      it('should filter users by role', async () => {
        const params = { role: 'admin' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3); // Admin User, two created in beforeEach
        expect(response.body.data.every(user => user.role === 'admin')).toBe(true);
      });

      it('should filter users by name', async () => {
        const lastTwo = await User.findAll({ order: [['name', 'DESC']], limit: 2 });
        await lastTwo[0].update({ name: 'Jhonn Doe' });
        await lastTwo[1].update({ name: 'Bob Jhonnsen' });

        const params = { name: 'Jhonn' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.data.some(user => user.name.includes('Jhonn'))).toBe(true);
      });

      it('should filter users by email domain', async () => {
        const params = { email: user.email };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].email).toBe(user.email);
      });

      it('should search users by name and email', async () => {
        let response;
        const params = { search: 'jhonn' };

        const prevEmail = user.email;
        await user.update({ email: '<EMAIL>' });
        response = await api.as(admin).get('/api/v1/users', params);
        expect(response.status).toBe(200);
        expect(response.body.data[0].email).toBe('<EMAIL>');

        await user.update({ email: prevEmail, name: 'Jhonn Doe' });
        response = await api.as(admin).get('/api/v1/users', params);
        expect(response.status).toBe(200);
        expect(response.body.data[0].name).toBe('Jhonn Doe');
      });

      it('should combine multiple filters', async () => {
        await admin.update({ name: 'Jhonn Doe' });
        const params = { role: 'admin', name: 'Jhonn' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].name).toBe('Jhonn Doe');
        expect(response.body.data[0].role).toBe('admin');
      });

      it('should not return user with less than configured number of variables', async () => {
        const params = { has_min_variables: 5 };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(0);

        const response2 = await api.as(admin).get('/api/v1/users', {});
        expect(response2.status).toBe(200);
        expect(response2.body.data).toHaveLength(6);
      });
    });

    describe('sorting', () => {
      it('should sort users by name ascending', async () => {
        const params = { sort: 'name', sort_direction: 'asc' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6);

        const names = response.body.data.map(user => user.name);
        const sortedNames = [...names].sort((a, b) => a.localeCompare(b, 'en'));
        expect(names).toEqual(sortedNames);
      });

      it('should sort users by name descending', async () => {
        const params = { sort: 'name', sort_direction: 'desc' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6);

        const names = response.body.data.map(user => user.name);
        const sortedNames = [...names].sort((a, b) => a.localeCompare(b, 'en')).reverse();
        expect(names).toEqual(sortedNames);
      });

      it('should sort users by email', async () => {
        const params = { sort: 'email', sort_direction: 'asc' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6);

        const emails = response.body.data.map(user => user.email);
        const sortedEmails = [...emails].sort();
        expect(emails).toEqual(sortedEmails);
      });

      it('reject invalid sort columns', async () => {
        const params = { sort: 'invalid_column', sort_direction: 'asc' };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('date filtering', () => {
      it('should filter users by created_after date', async () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const dateString = yesterday.toISOString().split('T')[0];

        const params = { created_after: dateString };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6); // All users created today
      });

      it('should filter users by created_before date', async () => {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dateString = tomorrow.toISOString().split('T')[0];

        const params = { created_before: dateString };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6); // All users created today
      });
    });

    describe('complex queries', () => {
      it('should handle complex query with multiple parameters', async () => {
        const params = {
          role: 'user',
          page: 1,
          limit: 2,
          sort: 'name',
          sort_direction: 'asc',
        };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.data.every(user => user.role === 'user')).toBe(true);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 2,
          total: 3,
          totalPages: 2,
        });
      });
    });

    describe('response format', () => {
      it('should return properly formatted user data', async () => {
        const params = { limit: 1 };
        const response = await api.as(admin).get('/api/v1/users', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);

        const user = response.body.data[0];
        expect(user).toHaveProperty('id');
        expect(user).toHaveProperty('name');
        expect(user).toHaveProperty('email');
        expect(user).toHaveProperty('role');
        expect(user).not.toHaveProperty('password');
        expect(user).not.toHaveProperty('password_digest');
      });
    });
  });

  describe('GET /api/v1/users/:id', () => {
    it('should return user data for valid ID', async () => {
      await UserProfileFactory.create({
        phone_number: '1234567890',
        location: 'New York',
        manager: 'John Doe',
        years_experience: 5,
        performance_rating: 4.5,
        last_promotion: '2024-01-01',
        education: 'Bachelor of Engineering',
        competencies: ['Leadership', 'Communication'],
        skills: ['JavaScript', 'React'],
        user_id: user.id,
      });

      const response = await api.as(admin).get(`/api/v1/users/${user.id}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toMatchObject({
        id: user.id,
        name: user.name,
        email: user.email,
        phone_number: '1234567890',
        location: 'New York',
        manager: 'John Doe',
        years_experience: 5,
        performance_rating: 4.5,
        last_promotion: '2024-01-01',
        education: 'Bachelor of Engineering',
        competencies: ['Leadership', 'Communication'],
        skills: ['JavaScript', 'React'],
        assessment_results: [],
        positions: [],
        performance_reviews: [],
        user_competencies: [],
      });
    });

    it('should return user with assessment results', async () => {
      await UserProfileFactory.create({ user_id: user.id });
      const assessmentResult = await UserAssessmentResultFactory.create({ user_id: user.id });

      const response = await api.as(admin).get(`/api/v1/users/${user.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data.assessment_results).toHaveLength(1);
      expect(response.body.data.assessment_results[0]).toMatchObject({
        id: assessmentResult.id,
        user_id: user.id,
        assessment: assessmentResult.assessment,
        aspect_name: assessmentResult.aspect_name,
        value_type: assessmentResult.value_type,
        value: assessmentResult.value,
      });
    });

    it('should return user with multiple positions', async () => {
      await UserProfileFactory.create({ user_id: user.id });
      const position1 = await UserPositionFactory.create({ user_id: user.id });
      const position2 = await UserPositionFactory.create({ user_id: user.id });

      const response = await api.as(admin).get(`/api/v1/users/${user.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data.positions).toHaveLength(2);
      expect(response.body.data.positions).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: position1.id,
            user_id: user.id,
            role_name: position1.role_name,
            department: position1.department,
            job_grade: position1.job_grade,
          }),
          expect.objectContaining({
            id: position2.id,
            user_id: user.id,
            role_name: position2.role_name,
            department: position2.department,
            job_grade: position2.job_grade,
          }),
        ]),
      );
    });

    it('should return user with performance reviews', async () => {
      await UserProfileFactory.create({ user_id: user.id });
      const position = await UserPositionFactory.create({ user_id: user.id });
      const performanceReview = await UserPerformanceReviewFactory.create({
        user_position_id: position.id,
      });

      const response = await api.as(admin).get(`/api/v1/users/${user.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data.performance_reviews).toHaveLength(1);
      expect(response.body.data.performance_reviews[0]).toMatchObject({
        id: performanceReview.id,
        user_position_id: position.id,
        review_type: performanceReview.review_type,
        review_result: performanceReview.review_result,
      });
    });

    it('should return user with competencies profiling', async () => {
      await UserProfileFactory.create({ user_id: user.id });
      const competenciesProfiling = await UserCompetenciesProfilingFactory.create({
        user_id: user.id,
      });

      const response = await api.as(admin).get(`/api/v1/users/${user.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data.user_competencies).toHaveLength(1);
      expect(response.body.data.user_competencies[0]).toMatchObject({
        id: competenciesProfiling.id,
        user_id: user.id,
        profiling_date: expect.any(String),
        assessors: competenciesProfiling.assessors,
        profile_as: competenciesProfiling.profile_as,
        readiness: competenciesProfiling.readiness,
        metadata: competenciesProfiling.metadata,
      });
    });

    it('should return comprehensive user data with all related records', async () => {
      await UserProfileFactory.create({
        phone_number: '1234567890',
        location: 'San Francisco',
        manager: 'Jane Smith',
        years_experience: 8,
        performance_rating: 4.8,
        last_promotion: '2023-06-01',
        education: 'Master of Computer Science',
        competencies: ['Technical Leadership', 'Strategic Planning'],
        skills: ['Python', 'AWS', 'Docker'],
        user_id: user.id,
      });

      const assessmentResult = await UserAssessmentResultFactory.create({ user_id: user.id });
      const position = await UserPositionFactory.create({ user_id: user.id });
      const performanceReview = await UserPerformanceReviewFactory.create({
        user_position_id: position.id,
      });
      const competenciesProfiling = await UserCompetenciesProfilingFactory.create({
        user_id: user.id,
      });

      const response = await api.as(admin).get(`/api/v1/users/${user.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject({
        id: user.id,
        name: user.name,
        email: user.email,
        phone_number: '1234567890',
        location: 'San Francisco',
        manager: 'Jane Smith',
        years_experience: 8,
        performance_rating: 4.8,
        last_promotion: '2023-06-01',
        education: 'Master of Computer Science',
        competencies: ['Technical Leadership', 'Strategic Planning'],
        skills: ['Python', 'AWS', 'Docker'],
      });

      expect(response.body.data.assessment_results).toHaveLength(1);
      expect(response.body.data.positions).toHaveLength(1);
      expect(response.body.data.performance_reviews).toHaveLength(1);
      expect(response.body.data.user_competencies).toHaveLength(1);

      expect(response.body.data.assessment_results[0].id).toBe(assessmentResult.id);
      expect(response.body.data.positions[0].id).toBe(position.id);
      expect(response.body.data.performance_reviews[0].id).toBe(performanceReview.id);
      expect(response.body.data.user_competencies[0].id).toBe(competenciesProfiling.id);
    });

    it('should return 404 for non-existent user', async () => {
      const response = await api.as(admin).get('/api/v1/users/999');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('User not found');
    });

    it('should return 401 without authentication', async () => {
      const response = await api.get('/api/v1/users/1');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 403 for non-admin user', async () => {
      const response = await api.as(user).get('/api/v1/users/1');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });
});
