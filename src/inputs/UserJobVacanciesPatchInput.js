const ApplicationInput = require('./ApplicationInput');

class UserJobVacanciesPatchInput extends ApplicationInput {
  /**
   * Define the JSON schema for user job vacancies patch validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        user_ids: {
          type: 'array',
          items: {
            type: 'integer',
            minimum: 1,
          },
          minItems: 1,
          maxItems: 100, // Reasonable limit for bulk operations
        },
        job_vacancy_pipeline_id: {
          type: 'integer',
          minimum: 1,
        },
        approved_by_id: {
          type: 'integer',
          minimum: 1,
          nullable: true,
        },
        approved_at: {
          type: 'string',
          format: 'date-time',
          nullable: true,
        },
        moved_to_next_pipeline_at: {
          type: 'string',
          format: 'date-time',
          nullable: true,
        },
        note: {
          type: 'string',
          maxLength: 5000, // Reasonable limit for notes
          nullable: true,
        },
      },
      required: ['user_ids', 'job_vacancy_pipeline_id'],
      additionalProperties: false,
    };
  }

  /**
   * Custom validation for business rules
   */
  validate() {
    // First run the base validation
    super.validate();

    const data = this.validatedData;

    // Custom validation: if approved_at is provided, approved_by_id should also be provided
    if (data.approved_at && !data.approved_by_id) {
      const InvalidError = require('../errors/InvalidError');
      throw new InvalidError('approved_by_id is required when approved_at is provided');
    }

    return true;
  }
}

module.exports = UserJobVacanciesPatchInput;
