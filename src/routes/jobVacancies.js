const express = require('express');
const resources = require('./resources');
const jobVacanciesController = require('../controllers/JobVacanciesController');
const jobVacancyPipelinesController = require('../controllers/JobVacancyPipelinesController');
const { authenticateToken, requirePermission } = require('../middlewares/auth');

const router = express.Router();

// Apply authentication to all routes in this file
router.use(authenticateToken);
router.use('/', resources('job_vacancies', jobVacanciesController));

// Add nested route for job vacancy pipelines
router.get(
  '/:id/pipelines',
  requirePermission('job_vacancy_pipelines:index'),
  jobVacancyPipelinesController.index,
);

module.exports = router;
