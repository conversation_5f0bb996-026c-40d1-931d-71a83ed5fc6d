const { Queue } = require('bullmq');
const { getRedisConnection } = require('../config/redis');
const config = require('../config/config');

class ApplicationJob {
  constructor() {
    console.log(`Initializing job: ${this.constructor.name}`);
    this.queueName = config.queue.name;
    this.config = config;
    this.redis = getRedisConnection();
    this.queue = new Queue(this.queueName, {
      connection: this.redis,
      defaultJobOptions: config.queue.defaultJobOptions,
    });
    this.attemptsMade = 0;
    this.maxAttempts = config.queue.defaultJobOptions.attempts;
  }

  /**
   * Sidekiq-like interface for enqueuing jobs
   * @param {Object} data - Job data to be processed
   * @param {Object} options - Job options (priority, delay, etc.)
   * @returns {Promise<Job>} The enqueued job
   */
  static async perform_async(data = {}, options = {}) {
    const instance = new this();
    const jobName = this.name;

    // Get job-specific retry configuration
    const retryConfig = instance.getRetryConfig();

    // Default options with priority support (lower number = higher priority)
    const jobOptions = {
      priority: options.priority || 0,
      delay: options.delay || 0,
      attempts: options.attempts || retryConfig.attempts || config.queue.defaultJobOptions.attempts,
      backoff: options.backoff || retryConfig.backoff || config.queue.defaultJobOptions.backoff,
      removeOnComplete: options.removeOnComplete || config.queue.defaultJobOptions.removeOnComplete,
      removeOnFail: options.removeOnFail || config.queue.defaultJobOptions.removeOnFail,
      ...options,
    };

    return instance.queue.add(jobName, data, jobOptions);
  }

  /**
   * Get retry configuration for this job
   * Override this method in subclasses to customize retry behavior
   * @returns {Object} Retry configuration
   */
  getRetryConfig() {
    return {
      attempts: config.queue.defaultJobOptions.attempts,
      backoff: config.queue.defaultJobOptions.backoff,
    };
  }

  setAttemptsMade(attemptsMade) {
    this.attemptsMade = attemptsMade;
  }

  setMaxAttempts(maxAttempts) {
    this.maxAttempts = maxAttempts;
  }

  /**
   * The main job processing method - must be implemented by subclasses
   * @param {Object} data - Job data
   * @returns {Promise<any>} Job result
   */
  async perform(_data) {
    throw new Error(`${this.constructor.name} must implement the perform method`);
  }

  /**
   * Hook called before job processing
   * @param {Object} data - Job data
   */
  async before_perform(_data) {
    // Override in subclasses if needed
  }

  /**
   * Hook called after successful job processing
   * @param {Object} data - Job data
   * @param {any} result - Job result
   */
  async after_perform(_data, _result) {
    // Override in subclasses if needed
  }

  /**
   * Hook called when job fails
   * @param {Object} data - Job data
   * @param {Error} error - The error that occurred
   */
  async on_failure(data, error) {
    console.error(`Job ${this.constructor.name} failed:`, error);
  }

  /**
   * Determine if a job should be retried based on the error
   * Override this method in subclasses for custom retry logic
   * @param {Error} error - The error that occurred
   * @param {number} attemptsMade - Number of attempts already made
   * @param {number} maxAttempts - Maximum attempts configured
   * @returns {boolean} Whether the job should be retried
   */
  shouldRetry(error, attemptsMade, maxAttempts) {
    // Default: retry all errors unless max attempts reached
    if (attemptsMade >= maxAttempts) {
      return false;
    }

    // Don't retry certain error types (override in subclasses)
    const nonRetryableErrors = ['ValidationError', 'AuthenticationError'];
    return !nonRetryableErrors.includes(error.name);
  }

  /**
   * Create different backoff strategies
   */
  static createBackoffStrategy(type, delay = 2000, maxDelay = 30000) {
    const strategies = {
      exponential: {
        type: 'exponential',
        delay,
        settings: { maxDelay },
      },
      fixed: {
        type: 'fixed',
        delay,
      },
      linear: {
        type: 'linear',
        delay,
        settings: { maxDelay },
      },
      custom: attemptsMade => Math.min(delay * Math.pow(2, attemptsMade - 1), maxDelay),
    };

    return strategies[type] || strategies.exponential;
  }

  /**
   * Get the queue instance
   * @returns {Queue} The Bull queue instance
   */
  getQueue() {
    return this.queue;
  }

  /**
   * Get the Redis connection
   * @returns {Redis} The Redis connection instance
   */
  getRedis() {
    return this.redis;
  }

  /**
   * Close the queue connection
   * Note: Redis connection is shared and managed centrally, so we don't close it here
   */
  async close() {
    await this.queue.close();
    // Redis connection is shared and managed by the centralized redis config
    // It will be closed when the application shuts down
  }

  setConfig(config) {
    this.config = config;
  }
}

module.exports = ApplicationJob;
