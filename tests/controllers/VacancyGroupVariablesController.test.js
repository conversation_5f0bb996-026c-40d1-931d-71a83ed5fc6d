const api = require('../utils/requestHelper');

const { UserFactory, JobVacancyFactory, JobTitleFactory } = require('../factories');
const {
  VacancyGroupVariable,
  JobGroupVariable,
  JobVariable,
  JobVacancyVariableBenchmark,
} = require('../../src/models');

describe('VacancyGroupVariablesController', () => {
  let admin;
  let user;
  let jobVacancy;
  let jobGroupVariable;
  let jobVariable;
  let jobVariable2;
  let vacancyGroupVariable;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();

    // Create test data
    const jobTitle = await JobTitleFactory.create();
    jobVacancy = await JobVacancyFactory.create({
      job_title_id: jobTitle.id,
      status: 'draft',
    });

    // Create job group variable
    jobGroupVariable = await JobGroupVariable.create({
      name: 'Test Group Variable',
      description: 'Test description',
      keywords: ['javascript', 'react', 'node'],
      order_level: 1,
    });

    jobVariable = await JobVariable.create({
      name: 'Test Variable',
      job_group_variable_id: jobGroupVariable.id,
      variable_type: 'numeric',
      filter_scales: { 1: 10, 2: 20, 3: 30, 4: 40, 5: 50, 6: 60, 7: 70 },
      mandatory: false,
    });

    jobVariable2 = await JobVariable.create({
      name: 'Test Variable 2',
      job_group_variable_id: jobGroupVariable.id,
      variable_type: 'numeric',
      filter_scales: { 1: 10, 2: 20, 3: 30, 4: 40, 5: 50, 6: 60, 7: 70 },
      mandatory: true,
    });

    await JobVacancyVariableBenchmark.create({
      job_vacancy_id: jobVacancy.id,
      job_variable_id: jobVariable.id,
      baseline_score: 55,
      baseline_scale: 5,
      configured_baseline_score: 55,
      configured_baseline_scale: 5,
    });

    await JobVacancyVariableBenchmark.create({
      job_vacancy_id: jobVacancy.id,
      job_variable_id: jobVariable2.id,
      baseline_score: 35,
      baseline_scale: 3,
      configured_baseline_score: 35,
      configured_baseline_scale: 3,
    });

    vacancyGroupVariable = await VacancyGroupVariable.create({
      job_vacancy_id: jobVacancy.id,
      job_group_variable_id: jobGroupVariable.id,
      keyword_match_count: 2,
      keyword_total_count: 3,
      match_type: 'weight',
      weight: null,
      avg_baseline_scale: 4,
      configured_baseline_scale: 4,
    });
  });

  describe('GET /api/v1/vacancy_group_variables', () => {
    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
        });

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
        });

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });
    });

    describe('validation', () => {
      it('should return 400 when job_vacancy_id is missing', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables');

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when job_vacancy_id is invalid', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: 'invalid',
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('successful responses', () => {
      it('should return vacancy group variables with correct format', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
        });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data).toHaveLength(1);

        const vgv = response.body.data[0];
        expect(vgv).toHaveProperty('id');
        expect(vgv).toHaveProperty('job_group_variable');
        expect(vgv.job_group_variable).toHaveProperty('id');
        expect(vgv.job_group_variable).toHaveProperty('name');
        expect(vgv.job_group_variable).toHaveProperty('description');
        expect(vgv).toHaveProperty('keyword_match_count');
        expect(vgv).toHaveProperty('keyword_total_count');
        expect(vgv).toHaveProperty('match_type');
        expect(vgv).toHaveProperty('weight');
        expect(vgv).toHaveProperty('filters');
        expect(vgv).toHaveProperty('order_level');
        expect(vgv.avg_baseline).toBe(4);
        expect(vgv.configured_baseline).toBe(4);

        const variables = vgv.filters;
        expect(variables).toHaveLength(2);

        const firstVar = variables.find(varObj => varObj.job_variable_id === jobVariable.id);
        expect(firstVar.value).toBe(55);
        expect(firstVar.mandatory).toBe(false);

        // Filter_scales as it is except for the one that matches the avg_baseline_scale
        // It will use the actual baseline_score instead of the filter_scales value
        expect(Object.keys(firstVar.filter_scales)).toHaveLength(7);
        expect(firstVar.filter_scales).toHaveProperty('3', 30);
        expect(firstVar.filter_scales).toHaveProperty('4', 55);
        expect(firstVar.filter_scales).toHaveProperty('5', 50);

        const secondVar = variables.find(varObj => varObj.job_variable_id === jobVariable2.id);
        expect(secondVar.value).toBe(35);
        expect(secondVar.mandatory).toBe(true);

        // Same with above
        expect(Object.keys(secondVar.filter_scales)).toHaveLength(7);
        expect(secondVar.filter_scales).toHaveProperty('3', 30);
        expect(secondVar.filter_scales).toHaveProperty('4', 35);
        expect(secondVar.filter_scales).toHaveProperty('5', 50);
      });

      it('should support pagination', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
          page: 1,
          limit: 10,
        });

        expect(response.status).toBe(200);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 10,
          total: 1,
        });
      });

      it('should support sorting', async () => {
        const response = await api.as(admin).get('/api/v1/vacancy_group_variables', {
          job_vacancy_id: jobVacancy.id,
          sort: 'id',
          sort_direction: 'desc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
      });
    });
  });

  describe('PATCH /api/v1/vacancy_group_variables/bulk_update', () => {
    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.patch('/api/v1/vacancy_group_variables/bulk_update', {
          vacancy_group_variables: [],
        });

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).patch('/api/v1/vacancy_group_variables/bulk_update', {
          vacancy_group_variables: [],
        });

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });
    });

    describe('validation', () => {
      it('should return 400 when vacancy_group_variables is missing', async () => {
        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', {});

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when vacancy_group_variables is empty', async () => {
        const response = await api.as(admin).patch('/api/v1/vacancy_group_variables/bulk_update', {
          vacancy_group_variables: [],
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when current jv is not draft or active', async () => {
        const testJobTitle = await JobTitleFactory.create();
        const testJobVacancy = await JobVacancyFactory.create({
          job_title_id: testJobTitle.id,
          status: 'calculating_match_scores',
        });
        const testJobGroupVariable = await JobGroupVariable.create({
          name: 'Test Group Variable',
          description: 'Test description',
          keywords: ['javascript', 'react', 'node'],
          order_level: 1,
        });
        const testVacancyGroupVariable = await VacancyGroupVariable.create({
          job_vacancy_id: testJobVacancy.id,
          job_group_variable_id: testJobGroupVariable.id,
          keyword_match_count: 2,
          keyword_total_count: 3,
          match_type: 'filter',
          weight: null,
        });
        const updateData = {
          vacancy_group_variables: [
            {
              id: testVacancyGroupVariable.id,
              match_type: 'weight',
              weight: 0.8,
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty(
          'error',
          'Cannot update this vacancy, current status: calculating_match_scores',
        );
      });
    });

    describe('successful updates', () => {
      it('should update vacancy group variable to weight match type', async () => {
        // Create a fresh record outside of transaction for this test
        const testJobTitle = await JobTitleFactory.create();
        const testJobVacancy = await JobVacancyFactory.create({
          job_title_id: testJobTitle.id,
          status: 'draft',
        });
        const testJobGroupVariable = await JobGroupVariable.create({
          name: 'Test Group Variable',
          description: 'Test description',
          keywords: ['javascript', 'react', 'node'],
          order_level: 1,
        });
        const testVacancyGroupVariable = await VacancyGroupVariable.create({
          job_vacancy_id: testJobVacancy.id,
          job_group_variable_id: testJobGroupVariable.id,
          keyword_match_count: 2,
          keyword_total_count: 3,
          match_type: 'filter',
          weight: null,
        });

        const updateData = {
          vacancy_group_variables: [
            {
              id: testVacancyGroupVariable.id,
              match_type: 'weight',
              weight: 0.8,
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(200);

        // Verify the update in database
        const updated = await VacancyGroupVariable.findByPk(testVacancyGroupVariable.id);
        expect(updated.match_type).toBe('weight');
        expect(updated.weight).toBe(0.8);
      });

      it('should update vacancy group variable to filter match type', async () => {
        // Create a fresh record outside of transaction for this test
        const testJobTitle = await JobTitleFactory.create();
        const testJobVacancy = await JobVacancyFactory.create({
          job_title_id: testJobTitle.id,
          status: 'draft',
        });
        const testJobGroupVariable = await JobGroupVariable.create({
          name: 'Test Group Variable 2',
          description: 'Test description 2',
          keywords: ['python', 'django', 'flask'],
          order_level: 2,
        });
        const testVacancyGroupVariable = await VacancyGroupVariable.create({
          job_vacancy_id: testJobVacancy.id,
          job_group_variable_id: testJobGroupVariable.id,
          keyword_match_count: 1,
          keyword_total_count: 3,
          match_type: 'weight',
          weight: 0.5,
        });

        const updateData = {
          vacancy_group_variables: [
            {
              id: testVacancyGroupVariable.id,
              match_type: 'filter',
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(200);

        // Verify the update in database
        const updated = await VacancyGroupVariable.findByPk(testVacancyGroupVariable.id);
        expect(updated.match_type).toBe('filter');
        expect(updated.weight).toBe(0);
      });

      it('should handle bulk update with multiple records', async () => {
        // Create additional test data
        const testJobTitle2 = await JobTitleFactory.create();
        const testJobVacancy2 = await JobVacancyFactory.create({
          job_title_id: testJobTitle2.id,
          status: 'draft',
        });
        const testJobGroupVariable2 = await JobGroupVariable.create({
          name: 'Test Group Variable 2',
          description: 'Test description 2',
          keywords: ['python', 'django'],
          order_level: 2,
        });
        const testVacancyGroupVariable2 = await VacancyGroupVariable.create({
          job_vacancy_id: testJobVacancy2.id,
          job_group_variable_id: testJobGroupVariable2.id,
          keyword_match_count: 1,
          keyword_total_count: 2,
          match_type: 'filter',
          weight: null,
          filters: [],
        });

        const updateData = {
          vacancy_group_variables: [
            {
              id: testVacancyGroupVariable2.id,
              match_type: 'weight',
              weight: 0.7,
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(200);
      });

      it('should update jvvb when configured_baseline is changed', async () => {
        const payload = {
          vacancy_group_variables: [
            {
              id: vacancyGroupVariable.id,
              match_type: 'weight',
              weight: 1,
              configured_baseline: 5,
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', payload);

        expect(response.status).toBe(200);

        const jvvb1 = await JobVacancyVariableBenchmark.findOne({
          where: { job_variable_id: jobVariable.id },
        });
        expect(jvvb1.configured_baseline_scale).toBe(5);
        expect(jvvb1.configured_baseline_score).toBe(50);
        // expect default value not changed
        expect(jvvb1.baseline_scale).toBe(5);
        expect(jvvb1.baseline_score).toBe(55);

        const jvvb2 = await JobVacancyVariableBenchmark.findOne({
          where: { job_variable_id: jobVariable2.id },
        });
        expect(jvvb2.configured_baseline_scale).toBe(5);
        expect(jvvb2.configured_baseline_score).toBe(50);
        // expect default value not changed
        expect(jvvb2.baseline_scale).toBe(3);
        expect(jvvb2.baseline_score).toBe(35);
      });

      it('should fallback to previous configured baseline when new one is blank', async () => {
        await vacancyGroupVariable.update({ configured_baseline_scale: 555 });
        const payload = {
          vacancy_group_variables: [
            {
              id: vacancyGroupVariable.id,
              match_type: 'weight',
              weight: 1,
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', payload);
        expect(response.status).toBe(200);

        await vacancyGroupVariable.reload();
        expect(vacancyGroupVariable.configured_baseline_scale).toBe(555);
      });
    });

    describe('error cases', () => {
      it('should return 404 for not found vacancy group variable ID', async () => {
        const updateData = {
          vacancy_group_variables: [
            {
              id: 99999, // Non-existent ID
              match_type: 'weight',
              weight: 0.5,
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(404);
        expect(response.body).toHaveProperty('error');
      });

      it('should return 400 when weight is missing for weight match type', async () => {
        const testJobTitle = await JobTitleFactory.create();
        const testJobVacancy = await JobVacancyFactory.create({ job_title_id: testJobTitle.id });
        const testJobGroupVariable = await JobGroupVariable.create({
          name: 'Test Group Variable',
          description: 'Test description',
          keywords: ['test'],
          order_level: 1,
        });
        const testVacancyGroupVariable = await VacancyGroupVariable.create({
          job_vacancy_id: testJobVacancy.id,
          job_group_variable_id: testJobGroupVariable.id,
          keyword_match_count: 1,
          keyword_total_count: 1,
          match_type: 'filter',
          weight: null,
          filters: [],
        });

        const updateData = {
          vacancy_group_variables: [
            {
              id: testVacancyGroupVariable.id,
              match_type: 'weight',
              // Missing weight
            },
          ],
        };

        const response = await api
          .as(admin)
          .patch('/api/v1/vacancy_group_variables/bulk_update', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error');
      });
    });
  });
});
