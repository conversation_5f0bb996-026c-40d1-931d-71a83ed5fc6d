const api = require('../utils/requestHelper');

const { UserJobVacancy, UserJobVacancyPipeline } = require('../../src/models');
const {
  UserFactory,
  JobTitleFactory,
  JobVacancyFactory,
  UserJobVacancyFactory,
  JobVacancyPipelineFactory,
} = require('../factories');

describe('UserJobVacanciesController - Pipeline Operations', () => {
  let admin;
  let user1;
  let user2;
  let jobTitle;
  let jobVacancy;
  let pipeline1;
  let pipeline2;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user1 = await UserFactory.create();
    user2 = await UserFactory.create();

    // Create job title and vacancy
    jobTitle = await JobTitleFactory.create();
    jobVacancy = await JobVacancyFactory.create({ job_title_id: jobTitle.id });

    // Create pipelines for the job vacancy
    pipeline1 = await JobVacancyPipelineFactory.createWithTrait('screening', {
      job_vacancy_id: jobVacancy.id,
    });
    pipeline2 = await JobVacancyPipelineFactory.createWithTrait('technical', {
      job_vacancy_id: jobVacancy.id,
    });

    // Create user job vacancies
    await UserJobVacancyFactory.create({
      user_id: user1.id,
      job_vacancy_id: jobVacancy.id,
      status: 'matched',
    });
    await UserJobVacancyFactory.create({
      user_id: user2.id,
      job_vacancy_id: jobVacancy.id,
      status: 'matched',
    });
  });

  describe('PATCH /api/v1/user_job_vacancies', () => {
    describe('successful updates', () => {
      it('should update users to a pipeline', async () => {
        const updateData = {
          user_ids: [user1.id, user2.id],
          job_vacancy_pipeline_id: pipeline1.id,
          approved_by_id: admin.id,
          approved_at: new Date().toISOString(),
          note: 'Moving to initial screening',
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveProperty('message');
        expect(response.body.data).toHaveProperty('updated_count', 2);

        // Verify user_job_vacancies status was updated
        const updatedUjv1 = await UserJobVacancy.findOne({
          where: { user_id: user1.id, job_vacancy_id: jobVacancy.id },
        });
        const updatedUjv2 = await UserJobVacancy.findOne({
          where: { user_id: user2.id, job_vacancy_id: jobVacancy.id },
        });

        expect(updatedUjv1.status).toBe(pipeline1.parameterized_name);
        expect(updatedUjv2.status).toBe(pipeline1.parameterized_name);

        // Verify user_job_vacancy_pipelines records were created
        const pipelineRecord1 = await UserJobVacancyPipeline.findOne({
          where: { user_id: user1.id, job_vacancy_pipeline_id: pipeline1.id },
        });
        const pipelineRecord2 = await UserJobVacancyPipeline.findOne({
          where: { user_id: user2.id, job_vacancy_pipeline_id: pipeline1.id },
        });

        expect(pipelineRecord1).toBeTruthy();
        expect(pipelineRecord1.approved_by_id).toBe(admin.id);
        expect(pipelineRecord1.note).toBe('Moving to initial screening');

        expect(pipelineRecord2).toBeTruthy();
        expect(pipelineRecord2.approved_by_id).toBe(admin.id);
        expect(pipelineRecord2.note).toBe('Moving to initial screening');
      });

      it('should update single user to pipeline', async () => {
        const updateData = {
          user_ids: [user1.id],
          job_vacancy_pipeline_id: pipeline2.id,
          note: 'Ready for technical interview',
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(200);
        expect(response.body.data.updated_count).toBe(1);

        // Verify status update
        const updatedUjv = await UserJobVacancy.findOne({
          where: { user_id: user1.id, job_vacancy_id: jobVacancy.id },
        });
        expect(updatedUjv.status).toBe(pipeline2.parameterized_name);
      });

      it('should handle minimal required data', async () => {
        const updateData = {
          user_ids: [user1.id],
          job_vacancy_pipeline_id: pipeline1.id,
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(200);
        expect(response.body.data.updated_count).toBe(1);
      });
    });

    describe('validation errors', () => {
      it('should return 400 for missing user_ids', async () => {
        const updateData = {
          job_vacancy_pipeline_id: pipeline1.id,
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for missing job_vacancy_pipeline_id', async () => {
        const updateData = {
          user_ids: [user1.id],
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for empty user_ids array', async () => {
        const updateData = {
          user_ids: [],
          job_vacancy_pipeline_id: pipeline1.id,
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for invalid user_ids', async () => {
        const updateData = {
          user_ids: ['invalid'],
          job_vacancy_pipeline_id: pipeline1.id,
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for invalid job_vacancy_pipeline_id', async () => {
        const updateData = {
          user_ids: [user1.id],
          job_vacancy_pipeline_id: 'invalid',
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when approved_at is provided without approved_by_id', async () => {
        const updateData = {
          user_ids: [user1.id],
          job_vacancy_pipeline_id: pipeline1.id,
          approved_at: new Date().toISOString(),
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error');
      });
    });

    describe('business logic errors', () => {
      it('should return 404 for non-existent pipeline', async () => {
        const updateData = {
          user_ids: [user1.id],
          job_vacancy_pipeline_id: 99999,
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(404);
        expect(response.body).toHaveProperty('error');
      });

      it('should return 400 for non-existent user', async () => {
        const updateData = {
          user_ids: [99999],
          job_vacancy_pipeline_id: pipeline1.id,
        };

        const response = await api.as(admin).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error');
      });
    });

    describe('authentication and authorization', () => {
      it('should return 401 if not authenticated', async () => {
        const updateData = {
          user_ids: [user1.id],
          job_vacancy_pipeline_id: pipeline1.id,
        };

        const response = await api.patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(401);
      });

      it('should return 403 if user lacks permission', async () => {
        const regularUser = await UserFactory.create({ role: 'user' });
        const updateData = {
          user_ids: [user1.id],
          job_vacancy_pipeline_id: pipeline1.id,
        };

        const response = await api.as(regularUser).patch('/api/v1/user_job_vacancies', updateData);

        expect(response.status).toBe(403);
      });
    });
  });
});
