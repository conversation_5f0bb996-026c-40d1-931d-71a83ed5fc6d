# Worker Prometheus Metrics Implementation

This document outlines the Prometheus metrics implementation for the Paragon Job
Worker.

## Files Modified/Created

### 1. `src/middlewares/worker-prometheus.js`

- **New file** - Dedicated Prometheus metrics for job worker
- Exports metrics for job processing, database calls, worker stats, etc.
- Includes Apdex calculation for background jobs (10s threshold)

### 2. `src/JobWorker.js`

- **Modified** - Integrated Prometheus metrics tracking
- Added metrics recording for job start/completion/failure/retry
- Added periodic stats updates every 10 seconds
- Added database query context tracking
- Added graceful shutdown for metrics

### 3. `src/WorkerHealthServer.js`

- **Modified** - Added `/metrics` endpoint for Prometheus scraping
- Health server now exposes worker metrics on the same port

### 4. `src/jobs/SetJobDescriptionJob.js`

- **Modified** - Added database metrics imports (example)
- Ready for enhanced database tracking in job classes

### 5. `grafana-paragon-worker-dashboard.json`

- **Modified** - Updated dashboard to use job worker metrics
- Changed from HTTP metrics to job processing metrics
- Updated panel titles and queries for job-specific data

## Metrics Exposed

### Job Processing Metrics

- `job_processing_duration_seconds` - Histogram of job processing times
- `job_processing_total` - Counter of total jobs processed
- `job_processing_rate` - Gauge of current job processing rate
- `job_apdex_score` - Apdex score for job performance

### Job Queue Metrics

- `job_queue_size` - Number of jobs waiting in queue
- `job_active_count` - Number of jobs currently being processed
- `job_retry_total` - Total number of job retries
- `job_failure_total` - Total number of job failures

### Worker Health Metrics

- `worker_uptime_seconds` - Worker uptime
- `worker_concurrency` - Worker concurrency setting
- `worker_memory_usage_bytes` - Memory usage by type (rss, heapUsed, etc.)

### Database Metrics

- `worker_db_calls_total` - Total database calls from worker
- `worker_db_call_duration_seconds` - Database call duration histogram

### Redis Metrics

- `worker_redis_calls_total` - Total Redis calls from worker
- `worker_redis_call_duration_seconds` - Redis call duration histogram

## Dashboard Panels

1. **Job Processing Latency (P50/P95/P99)** - Response time percentiles
2. **Job Failure Rate (%)** - Percentage of failed jobs
3. **Job Apdex Score** - Application performance index for jobs
4. **Job Processing Rate** - Jobs processed per second
5. **Job Performance Table** - Detailed breakdown by job type
6. **CPU Usage** - Container CPU usage
7. **Memory RSS** - Memory usage
8. **Database Calls RPS** - Database requests per second
9. **Database Calls Avg Duration** - Average database query time

## Usage

### Starting the Worker

```bash
npm run worker        # Production
npm run worker:dev    # Development with nodemon
```

### Accessing Metrics

- **Health Check**: `http://localhost:3001/health`
- **Prometheus Metrics**: `http://localhost:3001/metrics`
- **Worker Stats**: `http://localhost:3001/stats`

### Grafana Dashboard Import

1. Open Grafana
2. Go to Dashboards → Import
3. Copy content from `grafana-paragon-worker-dashboard.json`
4. Configure Prometheus datasource
5. Import dashboard

## Key Features

- **Automatic Tracking**: Jobs are automatically tracked when processed
- **Database Context**: DB queries are tagged with current job name
- **Apdex Calculation**: Performance score calculated with 10s threshold
- **Graceful Shutdown**: Metrics are properly cleaned up on worker stop
- **Kubernetes Ready**: Health endpoints support K8s probes
- **Job-Specific Labels**: All metrics include job name for filtering

## Configuration

The worker uses the existing configuration from `config/config.js`:

- Worker concurrency from `config.queue.concurrency`
- Health server port from `config.worker.healthPort` (defaults to 3001)
- Health server enabled via `config.worker.enableHealthServer`

## Next Steps

1. Deploy worker with metrics enabled
2. Configure Prometheus to scrape worker endpoints
3. Import Grafana dashboard
4. Set up alerts based on job failure rates or processing times
5. Enhance individual job classes with custom metrics if needed
