const client = require('prom-client');

// Create a Registry which registers the metrics
const register = new client.Registry();

// Add a default label which is added to all metrics
// Use environment variable for app label to distinguish between dev/prod deployments
const appLabel = process.env.PROMETHEUS_WORKER_APP_LABEL || 'paragon-worker';
register.setDefaultLabels({
  app: appLabel,
});

// Enable the collection of default metrics
client.collectDefaultMetrics({ register });

// Job Processing Metrics

// Job Processing Duration Histogram
const jobProcessingDuration = new client.Histogram({
  name: 'job_processing_duration_seconds',
  help: 'Duration of job processing in seconds',
  labelNames: ['job_name', 'status', 'worker_id'],
  buckets: [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10, 30, 60],
});

// Job Processing Counter
const jobProcessingTotal = new client.Counter({
  name: 'job_processing_total',
  help: 'Total number of jobs processed',
  labelNames: ['job_name', 'status', 'worker_id'],
});

// Job Processing Rate (Jobs per second)
const jobProcessingRate = new client.Gauge({
  name: 'job_processing_rate',
  help: 'Current job processing rate per second',
  labelNames: ['job_name', 'worker_id'],
});

// Job Queue Metrics
const jobQueueSize = new client.Gauge({
  name: 'job_queue_size',
  help: 'Number of jobs waiting in the queue',
  labelNames: ['queue_name'],
});

const jobActiveCount = new client.Gauge({
  name: 'job_active_count',
  help: 'Number of jobs currently being processed',
  labelNames: ['worker_id'],
});

// Job Retry Metrics
const jobRetryTotal = new client.Counter({
  name: 'job_retry_total',
  help: 'Total number of job retries',
  labelNames: ['job_name', 'worker_id'],
});

// Job Failure Metrics
const jobFailureTotal = new client.Counter({
  name: 'job_failure_total',
  help: 'Total number of job failures',
  labelNames: ['job_name', 'worker_id', 'error_type'],
});

// Worker Health Metrics
const workerUptime = new client.Gauge({
  name: 'worker_uptime_seconds',
  help: 'Worker uptime in seconds',
  labelNames: ['worker_id'],
});

const workerConcurrency = new client.Gauge({
  name: 'worker_concurrency',
  help: 'Worker concurrency setting',
  labelNames: ['worker_id'],
});

// Database calls from worker
const workerDbCallsTotal = new client.Counter({
  name: 'worker_db_calls_total',
  help: 'Total number of database calls from worker',
  labelNames: ['db_type', 'job_name'],
});

const workerDbCallDuration = new client.Histogram({
  name: 'worker_db_call_duration_seconds',
  help: 'Duration of database calls from worker in seconds',
  labelNames: ['db_type', 'job_name'],
  buckets: [0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1],
});

// Redis calls from worker
const workerRedisCallsTotal = new client.Counter({
  name: 'worker_redis_calls_total',
  help: 'Total number of Redis calls from worker',
  labelNames: ['operation', 'job_name'],
});

const workerRedisCallDuration = new client.Histogram({
  name: 'worker_redis_call_duration_seconds',
  help: 'Duration of Redis calls from worker in seconds',
  labelNames: ['operation', 'job_name'],
  buckets: [0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1],
});

// Memory usage specific to worker
const workerMemoryUsage = new client.Gauge({
  name: 'worker_memory_usage_bytes',
  help: 'Worker memory usage in bytes',
  labelNames: ['type', 'worker_id'],
});

// Job-specific metrics for Apdex-like calculation
const jobApdexScore = new client.Gauge({
  name: 'job_apdex_score',
  help: 'Apdex score for job performance (0-1)',
  labelNames: ['job_name', 'worker_id'],
});

// Register all metrics
register.registerMetric(jobProcessingDuration);
register.registerMetric(jobProcessingTotal);
register.registerMetric(jobProcessingRate);
register.registerMetric(jobQueueSize);
register.registerMetric(jobActiveCount);
register.registerMetric(jobRetryTotal);
register.registerMetric(jobFailureTotal);
register.registerMetric(workerUptime);
register.registerMetric(workerConcurrency);
register.registerMetric(workerDbCallsTotal);
register.registerMetric(workerDbCallDuration);
register.registerMetric(workerRedisCallsTotal);
register.registerMetric(workerRedisCallDuration);
register.registerMetric(workerMemoryUsage);
register.registerMetric(jobApdexScore);

// Job Apdex threshold in seconds (10s for background jobs)
const JOB_APDEX_THRESHOLD = 10;

// Store for calculating job rates and Apdex
const jobCounts = new Map();
const jobApdexCounts = new Map();

// Update rates and Apdex every 10 seconds
if (process.env.NODE_ENV !== 'test') {
  setInterval(() => {
    updateJobRatesAndApdex();
  }, 10000);
}

// Function to track job Apdex score
function trackJobApdex(duration, jobName, workerId) {
  const key = `${jobName}_${workerId}`;
  if (!jobApdexCounts.has(key)) {
    jobApdexCounts.set(key, { satisfied: 0, tolerating: 0, frustrated: 0, total: 0 });
  }
  const counts = jobApdexCounts.get(key);
  counts.total++;
  if (duration <= JOB_APDEX_THRESHOLD) {
    counts.satisfied++;
  } else if (duration <= JOB_APDEX_THRESHOLD * 4) {
    counts.tolerating++;
  } else {
    counts.frustrated++;
  }
}

// Function to update job rates and Apdex scores
function updateJobRatesAndApdex() {
  // Update job processing rates
  for (const [key, count] of jobCounts.entries()) {
    const [jobName, workerId] = key.split('_');
    const rps = count / 10; // 10-second intervals
    jobProcessingRate.labels(jobName, workerId).set(rps);
    jobCounts.set(key, 0); // Reset counter
  }

  // Calculate and update job Apdex scores
  for (const [key, counts] of jobApdexCounts.entries()) {
    const [jobName, workerId] = key.split('_');
    if (counts.total > 0) {
      const apdex = (counts.satisfied + counts.tolerating * 0.5) / counts.total;
      jobApdexScore.labels(jobName, workerId).set(apdex);

      // Reset counters for next period
      counts.satisfied = 0;
      counts.tolerating = 0;
      counts.frustrated = 0;
      counts.total = 0;
    }
  }
}

// Helper functions to record metrics
function recordJobStart(jobName, workerId) {
  jobActiveCount.labels(workerId).inc();
}

function recordJobCompletion(jobName, workerId, duration, success = true, errorType = null) {
  const status = success ? 'success' : 'failed';

  // Record basic metrics
  jobProcessingDuration.labels(jobName, status, workerId).observe(duration);
  jobProcessingTotal.labels(jobName, status, workerId).inc();
  jobActiveCount.labels(workerId).dec();

  // Track for rate calculation
  const key = `${jobName}_${workerId}`;
  jobCounts.set(key, (jobCounts.get(key) || 0) + 1);

  // Track Apdex
  trackJobApdex(duration, jobName, workerId);

  // Record failures with error type
  if (!success && errorType) {
    jobFailureTotal.labels(jobName, workerId, errorType).inc();
  }
}

function recordJobRetry(jobName, workerId) {
  jobRetryTotal.labels(jobName, workerId).inc();
}

function recordDbCall(dbType, durationSeconds, jobName = 'unknown') {
  workerDbCallsTotal.labels(dbType, jobName).inc();
  if (typeof durationSeconds === 'number') {
    workerDbCallDuration.labels(dbType, jobName).observe(durationSeconds);
  }
}

function recordRedisCall(operation, durationSeconds, jobName = 'unknown') {
  workerRedisCallsTotal.labels(operation, jobName).inc();
  if (typeof durationSeconds === 'number') {
    workerRedisCallDuration.labels(operation, jobName).observe(durationSeconds);
  }
}

function updateWorkerStats(workerId, uptime, concurrency, memoryUsage) {
  workerUptime.labels(workerId).set(uptime);
  workerConcurrency.labels(workerId).set(concurrency);

  if (memoryUsage) {
    workerMemoryUsage.labels('rss', workerId).set(memoryUsage.rss);
    workerMemoryUsage.labels('heapUsed', workerId).set(memoryUsage.heapUsed);
    workerMemoryUsage.labels('heapTotal', workerId).set(memoryUsage.heapTotal);
    workerMemoryUsage.labels('external', workerId).set(memoryUsage.external);
  }
}

function updateQueueStats(queueName, size) {
  jobQueueSize.labels(queueName).set(size);
}

// Metrics endpoint handler
const metricsHandler = async (_req, res) => {
  try {
    res.set('Content-Type', register.contentType);
    const metrics = await register.metrics();
    res.end(metrics);
  } catch (error) {
    res.status(500).end(error.message);
  }
};

// Export the metrics interface for JobWorker
module.exports = {
  register,
  metricsHandler,
  recordJobStart,
  recordJobCompletion,
  recordJobRetry,
  recordDbCall,
  recordRedisCall,
  updateWorkerStats,
  updateQueueStats,
  metrics: {
    jobProcessingDuration,
    jobProcessingTotal,
    jobProcessingRate,
    jobQueueSize,
    jobActiveCount,
    jobRetryTotal,
    jobFailureTotal,
    workerUptime,
    workerConcurrency,
    workerDbCallsTotal,
    workerDbCallDuration,
    workerRedisCallsTotal,
    workerRedisCallDuration,
    workerMemoryUsage,
    jobApdexScore,
  },
};
