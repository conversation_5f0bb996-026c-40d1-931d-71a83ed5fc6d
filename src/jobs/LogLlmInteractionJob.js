const ApplicationJob = require('./ApplicationJob');
const { LlmMetadata } = require('../models');

class LogLlmInteractionJob extends ApplicationJob {
  async perform(data) {
    const { request, responses, actionType } = data;

    if (!request || !responses || !actionType) {
      throw new Error('Missing required data for LogLlmInteractionJob');
    }

    await LlmMetadata.create({
      request,
      responses,
      action_type: actionType,
    });
  }
}

module.exports = LogLlmInteractionJob;
