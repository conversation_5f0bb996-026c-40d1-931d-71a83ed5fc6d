const ApiOutput = require('./ApiOutput');

class VacancyGroupVariableOutput extends ApiOutput {
  /**
   * Format a single vacancy group variable for output
   * @returns {Object} Formatted vacancy group variable data
   */
  format() {
    const vgv = this.data;

    return {
      id: vgv.id,
      job_group_variable: this.jobGroupVariableOutput(),
      keyword_match_count: vgv.keyword_match_count,
      keyword_total_count: vgv.keyword_total_count,
      match_type: vgv.match_type,
      weight: vgv.weight,
      order_level: vgv.jobGroupVariable?.order_level,
      bone_value: vgv.bone_value,
      default_weight: vgv.default_weight,
      avg_baseline: vgv.avg_baseline_scale,
      filters: this.filtersOutput(),
      configured_baseline: vgv.configured_baseline_scale,
    };
  }

  jobGroupVariableOutput() {
    if (!this.data.jobGroupVariable) return {};

    return {
      id: this.data.jobGroupVariable.id,
      name: this.data.jobGroupVariable.name,
      description: this.data.jobGroupVariable.description,
    };
  }

  filtersOutput() {
    if (!this.data.jobGroupVariable) return [];

    const jobVariables = this.data.jobGroupVariable.jobVariables || [];
    const baselineValues = this.options.baselineValues || [];

    return jobVariables.map(jobVariable => {
      const baselineValue = baselineValues.find(bv => bv.job_variable_id === jobVariable.id);
      const filterValue = baselineValue ? baselineValue.baseline_score : null;

      const filterScales = jobVariable.filter_scales;
      const avgBaselineScale = this.data.avg_baseline_scale;
      if (avgBaselineScale && avgBaselineScale > 0) filterScales[avgBaselineScale] = filterValue;

      return {
        job_variable_id: jobVariable.id,
        job_variable_name: jobVariable.name,
        job_variable_type: jobVariable.variable_type,
        value: filterValue,
        mandatory: jobVariable.mandatory,
        filter_scales: filterScales,
      };
    });
  }
}

module.exports = VacancyGroupVariableOutput;
