const express = require('express');
const resources = require('./resources');
const userPositionsController = require('../controllers/UserPositionsController');
const { authenticateToken } = require('../middlewares/auth');

const router = express.Router();
router.use(authenticateToken);
router.use('/', resources('user_positions', userPositionsController, { only: ['index'] }));

module.exports = router;
