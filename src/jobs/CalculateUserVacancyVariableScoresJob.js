const ApplicationJob = require('./ApplicationJob');
// const JobVacancyService = require('../services/JobVacancyService');

const {
  JobVacancy,
  VacancyGroupVariable,
  JobVariable,
  sequelize,
  transaction,
} = require('../models');

class CalculateUserVacancyVariableScoresJob extends ApplicationJob {
  async perform(data) {
    const { jobVacancyId } = data;
    // const jobVacancyService = new JobVacancyService();
    const jobVacancy = await JobVacancy.findByPk(jobVacancyId);
    if (!jobVacancy) throw new Error(`Job vacancy with id ${jobVacancyId} not found`);
    // const benchmarkIds = jobVacancy.related_user_ids || [];

    try {
      await transaction(async () => {
        // await this.calculateAllVariableMatchScores(jobVacancyId, benchmarkIds);
        // await jobVacancyService.averagingVarGroup(jobVacancyId);
        // await jobVacancyService.weightingMatchRate(jobVacancyId);

        // Since currently we don't need detailed data on jgv-jv level,
        // we can skip above steps for now
        await this.calculateFinalMatchRate(jobVacancyId);
        await jobVacancy.update({ status: 'active' });
      });
    } catch (error) {
      console.error('Error calculating user vacancy variable scores:', error);
      await jobVacancy.update({ status: 'draft' });
      throw error;
    }
  }

  async calculateAllVariableMatchScores(jobVacancyId, benchmarkIds) {
    try {
      const vgvs = await VacancyGroupVariable.findAll({
        where: { job_vacancy_id: jobVacancyId },
      });

      await Promise.all(
        vgvs.map(async vgv => {
          const jobVariables = await JobVariable.findAll({
            where: { job_group_variable_id: vgv.job_group_variable_id },
          });

          await Promise.all(
            jobVariables.map(async jobVariable => {
              await this.calculateUvvs(benchmarkIds, jobVacancyId, jobVariable.id);
            }),
          );
        }),
      );
    } catch (error) {
      console.error('Error calculating all variable match scores:', error);
      throw error;
    }
  }

  async calculateUvvs(benchmarkIds, jobVacancyId, jobVariableId) {
    const sql = `
      WITH user_variable_scores AS (
        SELECT u.id AS user_id,
          job_variables.id AS job_variable_id,
          job_variables.variable_type,
          job_variables.scoring_direction,
          CASE
            WHEN job_variables.variable_type = 'numeric' AND ujv.raw_value ~ '^[-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?$'
              THEN ujv.raw_value::DOUBLE PRECISION
            WHEN job_variables.variable_type = 'categorical'
              THEN (
                ujv.normalized_value::INTEGER / 7
              )::DOUBLE PRECISION -- Exact match only
            ELSE NULL
          END AS score
        FROM users u
        CROSS JOIN job_variables
        LEFT JOIN user_job_variables ujv
          ON ujv.user_id = u.id
          AND ujv.job_variable_id = job_variables.id
          AND ujv.deleted_at IS NULL
        WHERE u.deleted_at IS NULL
          AND job_variables.id = :job_variable_id
      )

      INSERT INTO user_vacancy_variable_scores (
        user_id,
        job_vacancy_id,
        job_variable_id,
        match_score,
        created_at,
        updated_at
      )

      SELECT uvs.user_id,
        (:job_vacancy_id)::int AS job_vacancy_id,
        uvs.job_variable_id,
        CASE
          WHEN COALESCE(bs.baseline_score, 0) = 0 THEN NULL
          WHEN COALESCE(uvs.score, 0) = 0 AND uvs.variable_type = 'numeric' THEN NULL
          WHEN uvs.scoring_direction = 'desc'
            THEN LEAST(100, GREATEST(0, ((2 * bs.baseline_score - uvs.score) / bs.baseline_score) * 100.0))
          ELSE LEAST(100, GREATEST(0, (uvs.score / bs.baseline_score) * 100.0))
        END AS match_score,
        NOW() AS created_at,
        NOW() AS updated_at
      FROM user_variable_scores uvs
      LEFT JOIN job_vacancy_variable_benchmarks bs
        ON bs.job_variable_id = uvs.job_variable_id
        AND bs.job_vacancy_id = :job_vacancy_id
        AND bs.deleted_at IS NULL

      ON CONFLICT (user_id, job_vacancy_id, job_variable_id)
      DO UPDATE SET match_score = EXCLUDED.match_score,
        updated_at = EXCLUDED.updated_at;
    `;

    try {
      await sequelize.query(sql, {
        replacements: {
          job_variable_id: jobVariableId,
          job_vacancy_id: jobVacancyId,
          user_ids: benchmarkIds,
        },
        type: sequelize.QueryTypes.RAW,
      });
    } catch (error) {
      console.error('Error calculating user vacancy variable scores:', error);
      throw error;
    }
  }

  async calculateFinalMatchRate(jobVacancyId) {
    const sql = `
      WITH RelevantJobVariables AS (
        SELECT
          jv.id AS job_variable_id,
          jv.variable_type,
          jv.scoring_direction,
          jv.mandatory,
          vgv.id AS vacancy_group_variable_id,
          vgv.job_group_variable_id,
          vgv.weight
        FROM job_variables jv
        JOIN vacancy_group_variables vgv
          ON vgv.job_group_variable_id = jv.job_group_variable_id
          AND vgv.deleted_at IS NULL
        WHERE
          jv.deleted_at IS NULL
          AND vgv.job_vacancy_id = :job_vacancy_id
      ),
      UserVariableRawScores AS (
        SELECT
          u.id AS user_id,
          rj.job_variable_id,
          rj.variable_type,
          rj.scoring_direction,
          rj.vacancy_group_variable_id,
          rj.job_group_variable_id,
          rj.weight,
          rj.mandatory,
          CASE
            WHEN rj.variable_type = 'numeric' AND ujv.raw_value ~ '^[-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?$'
              THEN ujv.raw_value::DOUBLE PRECISION
            WHEN rj.variable_type = 'categorical'
              THEN (ujv.normalized_value::INTEGER / 7)::DOUBLE PRECISION
            ELSE NULL
          END AS user_score,
          ujv.normalized_value AS user_scale
        FROM users u
        CROSS JOIN RelevantJobVariables rj
        LEFT JOIN user_job_variables ujv
          ON ujv.user_id = u.id
          AND ujv.job_variable_id = rj.job_variable_id
          AND ujv.deleted_at IS NULL
        WHERE u.deleted_at IS NULL
      ),
      VariableBaselines AS (
        SELECT
          job_variable_id,
          COALESCE(configured_baseline_score, baseline_score) AS baseline_score,
          COALESCE(configured_baseline_scale, baseline_scale) AS baseline_scale
        FROM job_vacancy_variable_benchmarks
        WHERE deleted_at IS NULL
          AND job_vacancy_id = :job_vacancy_id
      ),
      VariableMatchScores AS (
        SELECT
          uvrs.user_id,
          uvrs.vacancy_group_variable_id,
          uvrs.mandatory,
          uvrs.job_group_variable_id,
          uvrs.weight,
          CASE
            WHEN bs.baseline_score IS NULL OR bs.baseline_score = 0 THEN NULL
            WHEN uvrs.variable_type = 'numeric' AND (uvrs.user_score IS NULL OR uvrs.user_score = 0) THEN NULL
            WHEN uvrs.scoring_direction = 'desc'
              THEN LEAST(100.0, GREATEST(0.0, ((2 * bs.baseline_score - uvrs.user_score) / bs.baseline_score) * 100.0))
            ELSE LEAST(100.0, GREATEST(0.0, (uvrs.user_score / bs.baseline_score) * 100.0))
          END AS match_score,
          CASE
            WHEN COALESCE(bs.baseline_scale, 0) = 0 THEN NULL
            WHEN COALESCE(uvrs.user_scale, 0) = 0 THEN NULL
            ELSE uvrs.user_scale - bs.baseline_scale
          END AS scale_diff
        FROM UserVariableRawScores uvrs
        LEFT JOIN VariableBaselines bs ON bs.job_variable_id = uvrs.job_variable_id
      ),
      GroupScores AS (
        SELECT
          user_id,
          :job_vacancy_id AS job_vacancy_id,
          job_group_variable_id,
          vacancy_group_variable_id,
          weight,
          COALESCE(AVG(match_score), 0) AS group_score,
          COUNT(*) FILTER (WHERE match_score IS NULL AND mandatory = TRUE) AS incomplete_variables_count,
          COUNT(*) FILTER (WHERE scale_diff < :scale_diff_threshold) AS filtered_out_variable_scales_count
        FROM VariableMatchScores
        GROUP BY
          user_id,
          job_vacancy_id,
          vacancy_group_variable_id,
          job_group_variable_id,
          weight
      )

      INSERT INTO user_job_vacancies (
        user_id,
        job_vacancy_id,
        incomplete_variables,
        filtered_out_variable_scales,
        match_rate,
        status,
        created_at,
        updated_at,
        deleted_at
      )

      SELECT
        user_id,
        job_vacancy_id,
        SUM(incomplete_variables_count) AS incomplete_variables,
        SUM(filtered_out_variable_scales_count) AS filtered_out_variable_scales,
        SUM(group_score * weight) AS match_rate,
        CASE
          WHEN SUM(filtered_out_variable_scales_count) > 0 THEN 'not_matched'
          WHEN SUM(group_score * weight) >= :match_rate_threshold THEN 'matched'
          ELSE 'not_matched'
        END AS status,
        NOW() AS created_at,
        NOW() AS updated_at,
        NULL AS deleted_at
      FROM GroupScores
      GROUP BY user_id, job_vacancy_id

      ON CONFLICT (user_id, job_vacancy_id)
      DO UPDATE SET
        incomplete_variables = EXCLUDED.incomplete_variables,
        filtered_out_variable_scales = EXCLUDED.filtered_out_variable_scales,
        match_rate = EXCLUDED.match_rate,
        status = EXCLUDED.status,
        updated_at = EXCLUDED.updated_at,
        deleted_at = EXCLUDED.deleted_at;
    `;

    try {
      await sequelize.query(sql, {
        replacements: {
          job_vacancy_id: jobVacancyId,
          scale_diff_threshold: this.config.matchRateScaleDiffThreshold,
          match_rate_threshold: this.config.matchRateThreshold,
        },
        type: sequelize.QueryTypes.RAW,
      });
    } catch (error) {
      console.error('Error calculating final match rate:', error);
      throw error;
    }
  }
}

module.exports = CalculateUserVacancyVariableScoresJob;
