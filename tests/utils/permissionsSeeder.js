const { Role, Permission, RolePermission, sequelize } = require('../../src/models');

const rolePermissions = {
  admin: [
    'bones:index',
    'job_levels:index',
    'job_titles:index',
    'job_vacancies:index',
    'job_vacancies:create',
    'job_vacancies:show',
    'job_vacancies:update',
    'job_vacancies:destroy',
    'job_vacancy_pipelines:index',
    'user_job_vacancies:index',
    'user_job_vacancies:show',
    'user_job_vacancies:update',
    'user_positions:index',
    'users:index',
    'users:show',
    'vacancy_group_variables:index',
    'vacancy_group_variables:bulk_update',
    'work_areas:index',
  ],
  bgp: ['job_vacancies:index', 'job_vacancies:show'],
  user: [],
};

const seedPermissions = async () => {
  await Role.truncate({ cascade: true });
  await Permission.truncate({ cascade: true });
  await RolePermission.truncate({ cascade: true });

  const rolesRecords = [];
  const permissionsRecords = [];
  const rolePermissionsRecords = [];

  for (const roleName of Object.keys(rolePermissions)) {
    rolesRecords.push({ name: roleName });

    for (const permissionName of rolePermissions[roleName]) {
      const alreadyExist = permissionsRecords.find(p => p.name === permissionName);
      if (!alreadyExist) permissionsRecords.push({ name: permissionName });

      rolePermissionsRecords.push({ roleName, permissionName });
    }
  }

  await Role.bulkCreate(rolesRecords);
  await Permission.bulkCreate(permissionsRecords);

  const valuePlaceholders = rolePermissionsRecords.map(() => `(?, ?)`).join(', ');
  const replacements = rolePermissionsRecords.flatMap(rp => [rp.roleName, rp.permissionName]);

  const sql = `
    INSERT INTO role_permissions (
      role_id,
      permission_id,
      created_at,
      updated_at
    )

    SELECT r.id, p.id, NOW(), NOW()
    FROM (VALUES ${valuePlaceholders}) AS v(role_name, permission_name)
    INNER JOIN roles AS r ON v.role_name = r.name
    INNER JOIN permissions AS p ON v.permission_name = p.name;
  `;

  await sequelize.query(sql, {
    replacements,
    type: sequelize.QueryTypes.INSERT,
  });
};

module.exports = seedPermissions;
