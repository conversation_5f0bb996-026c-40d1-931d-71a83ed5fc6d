'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_group_variables', 'phrases', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('job_group_variables', 'jgv_code', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addIndex('job_group_variables', ['jgv_code'], {
      unique: true,
      name: 'job_group_variables_jgv_code_unique_constraint',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('job_group_variables', 'phrases');
    await queryInterface.removeColumn('job_group_variables', 'jgv_code');
  },
};
