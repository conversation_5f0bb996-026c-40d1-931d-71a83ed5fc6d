const AppService = require('./AppService');
const UserJobVacanciesRepository = require('../repositories/UserJobVacanciesRepository');
const {
  JobVacancyPipeline,
  UserJobVacancyPipeline,
  UserJobVacancy,
  User,
  sequelize,
  transaction,
} = require('../models');

class UserJobVacanciesService extends AppService {
  constructor() {
    super();
    this.repository = new UserJobVacanciesRepository();
  }

  /**
   * Get all user job vacancy recommendations with pagination and filtering
   * @param {Object} params - Query params (page, limit, job_vacancy_id, etc.)
   * @returns {Object} - UserJobVacancy records array and pagination info
   */
  async findAll(params = {}) {
    params['sort'] = params.sort || ['incomplete_variables', 'match_rate', 'id'];
    params['sort_direction'] = params.sort_direction || ['asc', 'desc', 'asc'];
    params['includes'] = ['user'];
    const { rows, pagination } = await this.repository.findAll(params);

    const ujvIds = [];
    const userIds = [];
    const jobVacancyIds = [];
    rows.forEach(row => {
      ujvIds.push(row.id);
      userIds.push(row.user_id);
      jobVacancyIds.push(row.job_vacancy_id);
    });

    const groupScoresById = await this.getGroupScores(ujvIds);
    const latestPositions = await this.getLatestPositions(userIds);
    const pipelineData = await this.getPipelineData(userIds, jobVacancyIds);

    return {
      userJobVacancies: rows,
      pagination,
      groupScoresById,
      latestPositions,
      pipelineData,
    };
  }

  /**
   * Find a specific user job vacancy recommendation by ID
   * @param {number} id - UserJobVacancy ID
   * @returns {Object} - UserJobVacancy with user and profile data
   */
  async findById(id) {
    // Validate ID is a valid integer
    const parsedId = parseInt(id, 10);
    this.assert(!isNaN(parsedId) && parsedId > 0, 'Invalid ID parameter');

    const userJobVacancy = await this.repository.model.findByPk(parsedId, {
      include: [
        {
          model: this.repository.model.sequelize.models.User,
          as: 'user',
          attributes: ['id', 'name'],
          include: [
            {
              model: this.repository.model.sequelize.models.UserProfile,
              as: 'profile',
              attributes: ['current_position'],
            },
          ],
        },
      ],
    });

    this.exists(userJobVacancy, 'User job vacancy recommendation not found');
    return userJobVacancy;
  }

  async getGroupScores(ujvIds) {
    if (ujvIds.length === 0) return {};

    const sqlGroupScores = `
      WITH grouped_variables AS (
        SELECT ujv.id AS user_job_vacancy_id,
          jgv.id AS job_group_variable_id,
          jsonb_agg(
            jsonb_build_object(
              'id', jv.id,
              'name', jv.name,
              'raw_value', ujvar.raw_value
            )
            ORDER BY jv.name
          ) AS variables
        FROM user_job_vacancies AS ujv
        JOIN vacancy_group_variables AS vgv
          ON vgv.job_vacancy_id = ujv.job_vacancy_id
          AND vgv.deleted_at IS NULL
        JOIN job_group_variables AS jgv
          ON jgv.id = vgv.job_group_variable_id
          AND jgv.deleted_at IS NULL
        JOIN job_variables AS jv
          ON jv.job_group_variable_id = jgv.id
          AND jv.deleted_at IS NULL
        LEFT JOIN user_job_variables AS ujvar
          ON ujvar.job_variable_id = jv.id
          AND ujvar.user_id = ujv.user_id
          AND ujvar.deleted_at IS NULL
        WHERE ujv.id IN (:ujv_ids)
          AND ujv.deleted_at IS NULL
        GROUP BY ujv.id, jgv.id
      )

      SELECT ujv.id,
        jsonb_agg(
          jsonb_build_object(
            'id', jgv.id,
            'name', jgv.name,
            'avg_match_score', uvgv.average_match_score,
            'variables', COALESCE(gv.variables, '[]'::jsonb)
          ) ORDER BY jgv.name
        ) AS group_scores
      FROM user_job_vacancies AS ujv
      JOIN vacancy_group_variables AS vgv
        ON vgv.job_vacancy_id = ujv.job_vacancy_id
        AND vgv.deleted_at IS NULL
      JOIN job_group_variables AS jgv
        ON jgv.id = vgv.job_group_variable_id
        AND jgv.deleted_at IS NULL
      LEFT JOIN user_vacancy_group_variables AS uvgv
        ON uvgv.vacancy_group_variable_id = vgv.id
        AND uvgv.user_id = ujv.user_id
        AND uvgv.deleted_at IS NULL
      LEFT JOIN grouped_variables AS gv
        ON gv.user_job_vacancy_id = ujv.id
        AND gv.job_group_variable_id = jgv.id
      WHERE ujv.id IN (:ujv_ids)
        AND ujv.deleted_at IS NULL
      GROUP BY ujv.id;
    `;

    const groupScores = await sequelize.query(sqlGroupScores, {
      replacements: { ujv_ids: ujvIds },
      type: sequelize.QueryTypes.SELECT,
    });

    return groupScores.reduce((acc, row) => {
      acc[row.id] = row.group_scores;
      return acc;
    }, {});
  }

  async getLatestPositions(userIds) {
    if (userIds.length === 0) return {};

    const sqlLatestPositions = `
      WITH latest_positions AS (
        SELECT user_id,
          role_name,
          department,
          division,
          ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY starts_at DESC) AS row_num
        FROM user_positions
        WHERE user_id IN (:user_ids)
          AND deleted_at IS NULL
      )

      SELECT user_id, role_name, department, division
      FROM latest_positions
      WHERE row_num = 1;
    `;

    const latestPositions = await sequelize.query(sqlLatestPositions, {
      replacements: { user_ids: userIds },
      type: sequelize.QueryTypes.SELECT,
    });

    return latestPositions.reduce((acc, row) => {
      acc[row.user_id] = row;
      return acc;
    }, {});
  }

  /**
   * Update users to a specific pipeline
   * @param {Object} data - Update data containing user_ids, job_vacancy_pipeline_id, etc.
   * @returns {Object} Update result
   */
  async updatePipelines(data) {
    const {
      user_ids,
      job_vacancy_pipeline_id,
      approved_by_id,
      approved_at,
      moved_to_next_pipeline_at,
      note,
    } = data;

    // Validate that the pipeline exists
    const pipeline = await JobVacancyPipeline.findByPk(job_vacancy_pipeline_id);
    this.exists(pipeline, 'Job vacancy pipeline not found');

    // Validate that all users exist
    const users = await User.findAll({
      where: { id: user_ids },
      attributes: ['id'],
    });
    this.assert(users.length === user_ids.length, 'One or more users not found');

    const result = await transaction(async t => {
      // Update user_job_vacancies status to pipeline parameterized name
      await UserJobVacancy.update(
        { status: pipeline.parameterized_name },
        {
          where: {
            user_id: user_ids,
            job_vacancy_id: pipeline.job_vacancy_id,
          },
          transaction: t,
        },
      );

      // Create or update user_job_vacancy_pipelines records
      const pipelineRecords = user_ids.map(user_id => ({
        user_id,
        job_vacancy_pipeline_id,
        created_by_id: approved_by_id, // Using approved_by_id as created_by_id for now
        approved_by_id,
        approved_at: approved_at ? new Date(approved_at) : null,
        moved_to_next_pipeline_at: moved_to_next_pipeline_at
          ? new Date(moved_to_next_pipeline_at)
          : null,
        note,
      }));

      // Use upsert to handle existing records
      const upsertPromises = pipelineRecords.map(record =>
        UserJobVacancyPipeline.upsert(record, {
          transaction: t,
          conflictFields: ['user_id', 'job_vacancy_pipeline_id'],
        }),
      );

      await Promise.all(upsertPromises);

      return { updated_count: user_ids.length };
    });

    return result;
  }

  /**
   * Get pipeline data for users
   * @param {Array} userIds - Array of user IDs
   * @param {number} jobVacancyId - Job vacancy ID
   * @returns {Object} Pipeline data grouped by user ID
   */
  async getPipelineData(userIds, jobVacancyIds) {
    if (userIds.length === 0) return {};
    if (jobVacancyIds) return {};

    const sqlPipelineData = `
      SELECT ujvp.user_id,
        jvp.parameterized_name AS job_vacancy_pipeline_parameterized_name,
        ujvp.created_by_id,
        ujvp.approved_by_id,
        ujvp.approved_at,
        ujvp.moved_to_next_pipeline_at,
        ujvp.note,
        ujvp.created_at
      FROM user_job_vacancy_pipelines ujvp
      JOIN job_vacancy_pipelines jvp ON ujvp.job_vacancy_pipeline_id = jvp.id
      WHERE ujvp.user_id IN (:user_ids)
        AND jvp.job_vacancy_id IN (:job_vacancy_ids)
        AND ujvp.deleted_at IS NULL
        AND jvp.deleted_at IS NULL
      ORDER BY jvp.order_level ASC, ujvp.created_at DESC
    `;

    const pipelineData = await sequelize.query(sqlPipelineData, {
      replacements: { user_ids: userIds, job_vacancy_ids: jobVacancyIds },
      type: sequelize.QueryTypes.SELECT,
    });

    return pipelineData.reduce((acc, row) => {
      if (!acc[row.user_id]) {
        acc[row.user_id] = [];
      }
      acc[row.user_id].push(row);
      return acc;
    }, {});
  }
}

module.exports = UserJobVacanciesService;
