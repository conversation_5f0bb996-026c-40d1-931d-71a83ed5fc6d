const LogLlmInteractionJob = require('../../src/jobs/LogLlmInteractionJob');
const { LlmMetadata } = require('../../src/models');

describe('LogLlmInteractionJob', () => {
  describe('perform', () => {
    it('should throw error when missing required data', async () => {
      const job = new LogLlmInteractionJob();

      await expect(job.perform({})).rejects.toThrow(
        'Missing required data for LogLlmInteractionJob',
      );
    });

    it('should log LLM interaction when all data is provided', async () => {
      const currentEntries = await LlmMetadata.count();
      expect(currentEntries).toBe(0);

      const job = new LogLlmInteractionJob();
      const data = {
        request: { model: 'gemini-2.5-flash' },
        responses: { candidates: [] },
        actionType: 'test_action',
      };

      await job.perform(data);

      const logEntry = await LlmMetadata.findOne();
      expect(logEntry).toBeDefined();
      expect(logEntry.request).toEqual(data.request);
      expect(logEntry.responses).toEqual(data.responses);
      expect(logEntry.action_type).toBe(data.actionType);
    });
  });
});
