const config = require('./config');
const { getRedisConnection } = require('./redis');
const redis = getRedisConnection();

const get = async key => {
  const cacheKey = generateKey(key);
  const value = await redis.get(cacheKey);
  return value ? JSON.parse(value) : null;
};

const set = async (key, value, expiry = 3600) => {
  const cacheKey = generateKey(key);
  return redis.set(cacheKey, JSON.stringify(value), 'EX', expiry);
};

const del = async key => {
  const cacheKey = generateKey(key);
  return redis.del(cacheKey);
};

const generateKey = key => {
  const app = 'paragon-app';
  const env = config.nodeEnv || 'development';
  return `${app}:${env}:${key}`;
};

module.exports = {
  get,
  set,
  del,
};
