const AppService = require('./AppService');
const VacancyGroupVariablesRepository = require('../repositories/VacancyGroupVariablesRepository');
const CalculateUserVacancyVariableScoresJob = require('../jobs/CalculateUserVacancyVariableScoresJob');
const {
  VacancyGroupVariable,
  JobVacancy,
  JobVacancyVariableBenchmark,
  JobVariable,
  transaction,
} = require('../models');

class VacancyGroupVariableService extends AppService {
  constructor() {
    super();
    this.repository = new VacancyGroupVariablesRepository();
  }

  /**
   * Find all vacancy group variables for a job vacancy with pagination and sorting
   * @param {Object} queryParams - Query parameters including job_vacancy_id
   * @returns {Object} Vacancy group variables with pagination info
   */
  async findAll(params = {}) {
    params['sort'] = params.sort || 'order_level';
    params['sort_direction'] = params.sort_direction || 'asc';
    params.includes = ['jobVariables'];

    const baselineValues = await JobVacancyVariableBenchmark.findAll({
      where: { job_vacancy_id: params.job_vacancy_id },
      attributes: ['job_variable_id', 'baseline_score'],
    });

    const { rows, pagination } = await this.repository.findAll(params);
    return { vacancyGroupVariables: rows, pagination, baselineValues };
  }

  /**
   * Bulk update vacancy group variables
   * @param {Array} vacancyGroupVariables - Array of vacancy group variables to update
   * @returns {Object} Update result
   */
  async bulkUpdate(vacancyGroupVariables) {
    const returnData = { updated_count: 0, updated_records: [] };
    let jobVacancyId;

    await transaction(async () => {
      const { existingVgvs, jobVacancy } =
        await this.validateAndFetchInitialData(vacancyGroupVariables);

      jobVacancyId = jobVacancy.id;
      const ids = existingVgvs.map(v => v.id);
      const inputMap = new Map(vacancyGroupVariables.map(v => [v.id, v]));

      await this.updateVacancyGroupVariables(existingVgvs, inputMap);
      await this.updateRelatedBenchmarks(existingVgvs, inputMap, jobVacancyId);

      await jobVacancy.update({ status: 'calculating_match_scores' });
      const updatedRecords = await VacancyGroupVariable.findAll({ where: { id: ids } });

      returnData.updated_count = updatedRecords.length;
      returnData.updated_records = updatedRecords;
    });

    await CalculateUserVacancyVariableScoresJob.perform_async({ jobVacancyId }, { attempts: 0 });
    return returnData;
  }

  async validateAndFetchInitialData(vacancyGroupVariables) {
    const ids = vacancyGroupVariables.map(vgv => vgv.id);
    const existingVgvs = await VacancyGroupVariable.findAll({
      where: { id: ids },
      attributes: [
        'id',
        'job_vacancy_id',
        'job_group_variable_id',
        'avg_baseline_scale',
        'configured_baseline_scale',
      ],
    });

    const jobVacancyId = existingVgvs[0]?.job_vacancy_id;
    const foundIds = new Set(existingVgvs.map(v => v.id));
    const missingIds = ids.filter(id => !foundIds.has(id));

    this.exists(
      missingIds.length === 0,
      `Vacancy group variables not found: ${missingIds.join(', ')}`,
    );

    this.assert(
      existingVgvs.every(vgv => vgv.job_vacancy_id === jobVacancyId),
      'All vacancy group variables must belong to the same job vacancy',
    );

    const jobVacancy = await JobVacancy.findByPk(jobVacancyId, {
      attributes: ['id', 'status'],
    });

    this.assert(
      jobVacancy && (jobVacancy.status === 'draft' || jobVacancy.status === 'active'),
      `Cannot update this vacancy, current status: ${jobVacancy?.status || 'not found'}`,
    );

    return { existingVgvs, jobVacancy };
  }

  async updateVacancyGroupVariables(existingVgvs, inputMap) {
    const now = new Date();
    const vgvUpdateRecords = existingVgvs.map(vgv => {
      const input = inputMap.get(vgv.id);
      const match_type = input.match_type || vgv.match_type;
      let weight = input.weight;

      if (match_type === 'weight') {
        this.assert(
          weight !== undefined && weight !== null,
          'Weight is required for weight match type',
        );
      } else if (match_type === 'filter') {
        weight = 0;
      }

      return {
        id: vgv.id,
        job_vacancy_id: vgv.job_vacancy_id,
        job_group_variable_id: vgv.job_group_variable_id,
        weight,
        match_type,
        configured_baseline_scale: input.configured_baseline || vgv.configured_baseline_scale,
        updated_at: now,
      };
    });

    await VacancyGroupVariable.bulkCreate(vgvUpdateRecords, {
      updateOnDuplicate: ['match_type', 'configured_baseline_scale', 'weight', 'updated_at'],
    });
  }

  async updateRelatedBenchmarks(existingVgvs, inputMap, jobVacancyId) {
    const now = new Date();
    const vgvMap = new Map(existingVgvs.map(vgv => [vgv.job_group_variable_id, vgv]));
    const jobGroupVariableIds = [...vgvMap.keys()];

    const jobVariables = await JobVariable.findAll({
      where: { job_group_variable_id: jobGroupVariableIds },
      attributes: ['id', 'job_group_variable_id', 'filter_scales'],
    });

    const jvvbUpdateRecords = jobVariables.map(jobVariable => {
      const vgv = vgvMap.get(jobVariable.job_group_variable_id);
      const input = inputMap.get(vgv.id);
      const configuredBaselineScale = input.configured_baseline;
      const filterScales = jobVariable.filter_scales;
      const configuredBaselineScore = filterScales[configuredBaselineScale];
      const shouldUseConfigured = vgv.avg_baseline_scale !== configuredBaselineScale;

      return {
        job_vacancy_id: jobVacancyId,
        job_variable_id: jobVariable.id,
        configured_baseline_scale: shouldUseConfigured
          ? configuredBaselineScale
          : vgv.avg_baseline_scale,
        configured_baseline_score: shouldUseConfigured
          ? configuredBaselineScore
          : filterScales[vgv.avg_baseline_scale],
        updated_at: now,
      };
    });

    if (jvvbUpdateRecords.length > 0) {
      await JobVacancyVariableBenchmark.bulkCreate(jvvbUpdateRecords, {
        updateOnDuplicate: ['configured_baseline_scale', 'configured_baseline_score', 'updated_at'],
      });
    }
  }
}

module.exports = VacancyGroupVariableService;
