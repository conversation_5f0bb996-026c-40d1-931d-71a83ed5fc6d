const ApiOutput = require('./ApiOutput');

class UserJobVacancyOutput extends ApiOutput {
  /**
   * Format the UserJobVacancy data for the API response.
   * @param {Object} item - A UserJobVacancy model instance with nested user and profile data (for array formatting)
   * @returns {Object} Formatted data.
   */
  format() {
    const record = this.data;
    const user = record.user || {};
    const latestPositions = this.options.latestPositions || {};
    const latestPosition = latestPositions[user.id] || {};

    return {
      id: record.id,
      user: {
        id: user.id,
        name: user.name,
        role: latestPosition.role_name || null,
        division: latestPosition.division || null,
        department: latestPosition.department || null,
      },
      status: record.status,
      match_rate: this.matchRateOutput(),
      variable_groups: this.variableGroupsOutput(),
      user_job_vacancy_pipelines: this.userJobVacancyPipelinesOutput(),
    };
  }

  matchRateOutput() {
    const roundedDecimal = Math.round(this.data.match_rate * 100) / 100;
    return Math.floor(roundedDecimal);
  }

  variableGroupsOutput() {
    const groupScoresById = this.options.groupScoresById || {};
    const currentData = groupScoresById[this.data.id] || [];

    return currentData.map(group => {
      const variables = group.variables || [];

      return {
        id: group.id,
        name: group.name,
        avg_match_score: Math.round(group.avg_match_score * 100) / 100,
        variables: variables.map(variable => ({
          id: variable.id,
          name: variable.name,
          raw_value: variable.raw_value || 'N/A',
        })),
      };
    });
  }

  userJobVacancyPipelinesOutput() {
    const pipelineData = this.options.pipelineData || {};
    const currentUserPipelines = pipelineData[this.data.user_id] || [];

    return currentUserPipelines.map(pipeline => ({
      job_vacancy_pipeline_parameterized_name: pipeline.job_vacancy_pipeline_parameterized_name,
      created_by_id: pipeline.created_by_id,
      approved_by_id: pipeline.approved_by_id,
      approved_at: pipeline.approved_at,
      moved_to_next_pipeline_at: pipeline.moved_to_next_pipeline_at,
      note: pipeline.note,
      created_at: pipeline.created_at,
    }));
  }
}

module.exports = UserJobVacancyOutput;
