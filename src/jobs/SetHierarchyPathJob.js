const ApplicationJob = require('./ApplicationJob');
const { sequelize, transaction } = require('../models');

class SetHierarchyPath<PERSON>ob extends ApplicationJob {
  async perform(data) {
    const {
      user_id: userId,
      old_path: oldPath,
      old_supervisor_id: oldSupervisorId,
      new_supervisor_id: newSupervisorId,
    } = data;

    if (oldSupervisorId && newSupervisorId && oldSupervisorId === newSupervisorId) return;
    if (!userId) throw new Error('user_id is required for SetHierarchyPathJob');

    const lockKey = 'set_hierarchy_path_job';
    let lockAcquired = false;

    try {
      await sequelize.query('SELECT pg_advisory_lock(hashtext(:lockKey)) as lock_acquired', {
        replacements: { lockKey },
        type: sequelize.QueryTypes.SELECT,
      });
      lockAcquired = true;

      const sql = `
        WITH user_info AS (
          SELECT users.id,
            users.hierarchy_path AS current_path,
            COALESCE(
              supervisor.hierarchy_path,
              supervisor.id::text::ltree
            ) AS supervisor_path
          FROM users
          LEFT JOIN users supervisor
            ON supervisor.id = :newSupervisorId
            AND supervisor.deleted_at IS NULL
          WHERE users.deleted_at IS NULL
            AND users.id = :userId
        ), validation AS (
          SELECT user_info.*,
            CASE 
              WHEN :newSupervisorId IS NULL
                THEN :userId::text::ltree
              WHEN user_info.supervisor_path IS NULL
                THEN NULL -- supervisor not found
              WHEN :oldPath IS NOT NULL AND user_info.supervisor_path <@ :oldPath::ltree
                THEN NULL -- circular dependency
              ELSE user_info.supervisor_path || :userId::text::ltree
            END AS new_path,
            CASE
              WHEN :newSupervisorId IS NOT NULL AND user_info.supervisor_path IS NULL
                THEN 'SUPERVISOR_NOT_FOUND'
              WHEN :oldPath IS NOT NULL AND user_info.supervisor_path <@ :oldPath::ltree
                THEN 'CIRCULAR_DEPENDENCY'
              ELSE 'SUCCESS'
            END AS validation_status
          FROM user_info
        ), update_result AS (
          UPDATE users
          SET
            hierarchy_path = CASE
              WHEN users.id = :userId THEN 
                (SELECT new_path FROM validation WHERE new_path IS NOT NULL)
              WHEN :oldPath IS NOT NULL AND hierarchy_path = :oldPath THEN 
                (SELECT new_path FROM validation WHERE new_path IS NOT NULL)
              WHEN :oldPath IS NOT NULL THEN
                (SELECT new_path FROM validation WHERE new_path IS NOT NULL) || 
                subpath(hierarchy_path, nlevel(:oldPath::ltree))
              ELSE
                (SELECT new_path FROM validation WHERE new_path IS NOT NULL) || 
                subpath(hierarchy_path, nlevel((SELECT current_path FROM validation)::ltree))
            END
          FROM validation
          WHERE validation.new_path IS NOT NULL
            AND users.deleted_at IS NULL
            AND (
              users.id = :userId OR
              users.hierarchy_path <@ (SELECT current_path FROM validation)::ltree
            )
          RETURNING  users.id, users.hierarchy_path
        ), result_summary AS (
          SELECT 
            COALESCE((SELECT COUNT(*) FROM update_result), 0) as updated_count,
            COALESCE((SELECT validation_status FROM validation LIMIT 1), 'USER_NOT_FOUND') as status
        )

        SELECT * FROM result_summary;
      `;

      const result = await transaction(async () => {
        return await sequelize.query(sql, {
          replacements: {
            userId,
            oldPath,
            newSupervisorId,
          },
          type: sequelize.QueryTypes.SELECT,
        });
      });

      const { updated_count, status } = result[0];
      this.validateStatus(status, userId, newSupervisorId);
      return { updated_count, status };
    } catch (error) {
      console.error('Error setting hierarchy path:', error);
      throw error;
    } finally {
      if (lockAcquired) await this.releaseLock(lockKey);
    }
  }

  validateStatus(status, userId, newSupervisorId) {
    if (status === 'SUPERVISOR_NOT_FOUND') {
      throw new Error(`Supervisor with ID ${newSupervisorId} not found`);
    }

    if (status === 'CIRCULAR_DEPENDENCY') {
      throw new Error(
        'Circular dependency detected. A user cannot be supervised by one of their own descendants.',
      );
    }

    if (status === 'USER_NOT_FOUND') {
      throw new Error(`User with ID ${userId} not found`);
    }

    if (status === 'NO_UPDATES') {
      console.warn(`No updates needed for user ${userId}`);
    }
  }

  async releaseLock(lockKey) {
    try {
      await sequelize.query('SELECT pg_advisory_unlock(hashtext(:lockKey))', {
        replacements: { lockKey },
        type: sequelize.QueryTypes.SELECT,
      });
    } catch (unlockError) {
      console.error('Error releasing advisory lock:', unlockError);
    }
  }
}

module.exports = SetHierarchyPathJob;
