'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_vacancy_variable_benchmarks', 'configured_baseline_scale', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    await queryInterface.addColumn('job_vacancy_variable_benchmarks', 'configured_baseline_score', {
      type: Sequelize.DOUBLE,
      allowNull: true,
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn(
      'job_vacancy_variable_benchmarks',
      'configured_baseline_scale',
    );
    await queryInterface.removeColumn(
      'job_vacancy_variable_benchmarks',
      'configured_baseline_score',
    );
  },
};
