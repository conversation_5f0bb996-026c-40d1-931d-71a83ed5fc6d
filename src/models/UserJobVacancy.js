'use strict';
const AppModel = require('./AppModel');

class UserJobVacancy extends AppModel {
  static associate(models) {
    this.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });

    this.belongsTo(models.JobVacancy, {
      foreignKey: 'job_vacancy_id',
      as: 'jobVacancy',
    });
  }

  static options() {
    return {
      tableName: 'user_job_vacancies',
    };
  }
}

module.exports = UserJobVacancy;
