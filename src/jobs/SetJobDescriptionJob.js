const { QdrantClient } = require('@qdrant/js-client-rest');
const ApplicationJob = require('./ApplicationJob');
const SetVacancyGroupVariablesJob = require('./SetVacancyGroupVariablesJob');
const { JobVacancy, transaction } = require('../models');
const OnetService = require('../services/OnetService');
const JobTitleService = require('../services/JobTitleService');
const GenerateJobDescService = require('../services/JobVacancy/GenerateJobDescService');
const GoogleAiService = require('../services/external/GoogleAiService');
const config = require('../config/config');

class SetJobDescriptionJob extends ApplicationJob {
  constructor() {
    super();
    this.onetService = new OnetService();
    this.googleAiService = new GoogleAiService();
    this.qdrantClient = new QdrantClient({
      url: config.qdrantUrl,
      apiKey: config.qdrantApiKey,
    });
    this.generateJobDescService = new GenerateJobDescService({
      onetService: this.onetService,
      googleAiService: this.googleAiService,
      qdrantClient: this.qdrantClient,
    });
    this.jobTitleService = new JobTitleService();
  }

  /**
   * Process the job description generation
   * @param {Object} data - Job data containing vacancy_id
   * @returns {Promise<Object>} Job result
   */
  async perform(data) {
    const { vacancy_id } = data;

    if (!vacancy_id) {
      throw new Error('vacancy_id is required for SetJobDescriptionJob');
    }

    // Fetch the vacancy with its related data
    const vacancy = await JobVacancy.findByPk(vacancy_id, {
      include: ['jobLevel'],
    });

    if (!vacancy) {
      throw new Error(`Job vacancy with id ${vacancy_id} not found`);
    }

    try {
      const jobTitleName = vacancy.name;
      const relatedUserIds = vacancy.related_user_ids;
      const jobLevelName = vacancy.jobLevel ? vacancy.jobLevel.name : '';
      const roleSummary = vacancy.role_summary;

      const generatedJobDesc = await this.generateJobDescService.generateJobDesc(
        jobTitleName,
        relatedUserIds,
        jobLevelName,
        roleSummary,
      );

      const mergedDescriptions = {
        ...vacancy.detailed_descriptions,
        ...generatedJobDesc.jobDescription,
      };

      await transaction(async () => {
        await vacancy.update({
          job_desc: generatedJobDesc.jobDescription.key_responsibilities,
          related_onetsoc_codes: generatedJobDesc.onetsocCodes,
          status: 'generating_job_variables',
          detailed_descriptions: mergedDescriptions,
        });

        await this.jobTitleService.upsertPrefilledDetails({
          id: vacancy.job_title_id,
          name: vacancy.name,
          prefilled_details: {
            job_level_id: vacancy.job_level_id,
            role_summary: vacancy.role_summary,
            key_responsibilities: mergedDescriptions.key_responsibilities,
            qualifications: mergedDescriptions.qualifications,
            competencies: mergedDescriptions.competencies,
            success_metrics: mergedDescriptions.success_metrics,
            work_inputs: mergedDescriptions.work_inputs,
            work_outputs: mergedDescriptions.work_outputs,
          },
        });
      });

      await SetVacancyGroupVariablesJob.perform_async({ vacancy_id: vacancy.id });
    } catch (error) {
      if (process.env.NODE_ENV !== 'test') {
        console.error(`Error generating job description for vacancy ${vacancy_id}:`, error);
      }

      // update to draft only when reached retry maxAttempts
      if (!this.shouldRetry(error, this.attemptsMade, this.maxAttempts)) {
        await vacancy.update({ status: 'draft' });
      }

      throw error;
    }
  }

  /**
   * Hook called before job processing
   * @param {Object} data - Job data
   */
  async before_perform(data) {
    console.log(`Starting SetJobDescriptionJob for vacancy ${data.vacancy_id}`);
  }

  /**
   * Hook called after successful job processing
   * @param {Object} data - Job data
   * @param {any} result - Job result
   */
  async after_perform(data, _result) {
    console.log(`Completed SetJobDescriptionJob for vacancy ${data.vacancy_id}`);
  }

  /**
   * Hook called when job fails
   * @param {Object} data - Job data
   * @param {Error} error - The error that occurred
   */
  async on_failure(data, error) {
    console.error(`SetJobDescriptionJob failed for vacancy ${data.vacancy_id}:`, error);

    // Additional error handling could be added here, such as:
    // - Sending notifications
    // - Updating external systems
    // - Logging to error tracking services
  }
}

module.exports = SetJobDescriptionJob;
