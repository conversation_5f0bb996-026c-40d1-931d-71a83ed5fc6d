// Mock dependencies first
jest.mock('bullmq');
jest.mock('../../src/config/redis');
jest.mock('../../src/config/config');
jest.mock('../../src/jobs');
jest.mock('../../src/jobs/SetJobDescriptionJob');
jest.mock('../../src/jobs/SetVacancyGroupVariablesJob');
jest.mock('../../src/WorkerHealthServer');

const JobWorker = require('../../src/JobWorker');
const { Worker } = require('bullmq');

// Mock the centralized Redis and jobs
const { getRedisConnection, closeRedisConnection } = require('../../src/config/redis');
const config = require('../../src/config/config');
const jobClasses = require('../../src/jobs');

describe('JobWorker', () => {
  let worker;
  let mockRedis;
  let mockBullWorker;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock config
    config.queue = {
      concurrency: 10,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
      },
      stalledInterval: 30000,
      maxStalledCount: 1,
      name: 'default',
    };
    config.worker = {
      enableHealthServer: false,
    };

    // Mock Redis
    mockRedis = {
      quit: jest.fn().mockResolvedValue(undefined),
    };
    getRedisConnection.mockReturnValue(mockRedis);
    closeRedisConnection.mockResolvedValue(undefined);

    // Mock job classes with default implementations
    jobClasses.SetJobDescriptionJob = jest.fn().mockImplementation(() => ({
      before_perform: jest.fn().mockResolvedValue(undefined),
      perform: jest.fn().mockResolvedValue({ status: 'completed' }),
      after_perform: jest.fn().mockResolvedValue(undefined),
      on_failure: jest.fn().mockResolvedValue(undefined),
      close: jest.fn().mockResolvedValue(undefined),
      setAttemptsMade: jest.fn(),
      setMaxAttempts: jest.fn(),
    }));
    jobClasses.SetVacancyGroupVariablesJob = jest.fn().mockImplementation(() => ({
      before_perform: jest.fn().mockResolvedValue(undefined),
      perform: jest.fn().mockResolvedValue({ status: 'completed' }),
      after_perform: jest.fn().mockResolvedValue(undefined),
      on_failure: jest.fn().mockResolvedValue(undefined),
      close: jest.fn().mockResolvedValue(undefined),
      setAttemptsMade: jest.fn(),
      setMaxAttempts: jest.fn(),
    }));

    // Mock Bull Worker
    mockBullWorker = {
      on: jest.fn(),
      close: jest.fn().mockResolvedValue(undefined),
      closing: false,
    };
    Worker.mockImplementation(() => mockBullWorker);

    worker = new JobWorker();
  });

  describe('constructor', () => {
    it('should initialize with default values', () => {
      expect(worker.redis).toBe(mockRedis);
      expect(worker.worker).toBeNull();
      expect(worker.isShuttingDown).toBe(false);
      expect(worker.jobClasses).toHaveProperty('SetJobDescriptionJob');
      expect(worker.jobClasses).toHaveProperty('SetVacancyGroupVariablesJob');
    });
  });

  describe('start', () => {
    it('should start the worker with correct configuration', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await worker.start();

      expect(Worker).toHaveBeenCalledWith(
        'default',
        expect.any(Function),
        expect.objectContaining({
          connection: mockRedis,
          concurrency: 10,
        }),
      );

      expect(mockBullWorker.on).toHaveBeenCalledWith('completed', expect.any(Function));
      expect(mockBullWorker.on).toHaveBeenCalledWith('failed', expect.any(Function));
      expect(mockBullWorker.on).toHaveBeenCalledWith('error', expect.any(Function));
      expect(mockBullWorker.on).toHaveBeenCalledWith('stalled', expect.any(Function));
      expect(mockBullWorker.on).toHaveBeenCalledWith('progress', expect.any(Function));

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Starting BullMQ worker'));
      expect(consoleSpy).toHaveBeenCalledWith('📊 Worker configuration:');
      expect(consoleSpy).toHaveBeenCalledWith('   - Concurrency: 10');
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Worker'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('started successfully'));

      consoleSpy.mockRestore();
    });

    it('should setup graceful shutdown handlers', async () => {
      const processOnSpy = jest.spyOn(process, 'on').mockImplementation();

      await worker.start();

      expect(processOnSpy).toHaveBeenCalledWith('SIGTERM', expect.any(Function));
      expect(processOnSpy).toHaveBeenCalledWith('SIGINT', expect.any(Function));
      expect(processOnSpy).toHaveBeenCalledWith('SIGUSR2', expect.any(Function));
      expect(processOnSpy).toHaveBeenCalledWith('uncaughtException', expect.any(Function));
      expect(processOnSpy).toHaveBeenCalledWith('unhandledRejection', expect.any(Function));

      processOnSpy.mockRestore();
    });
  });

  describe('job processing', () => {
    let jobProcessor;
    let mockJob;

    beforeEach(async () => {
      await worker.start();

      // Get the job processor function passed to Worker
      const workerCall = Worker.mock.calls[0];
      jobProcessor = workerCall[1];

      mockJob = {
        name: 'SetJobDescriptionJob',
        data: { vacancy_id: 1 },
      };
    });

    it('should process known job types', async () => {
      const mockJobInstance = {
        before_perform: jest.fn().mockResolvedValue(undefined),
        perform: jest.fn().mockResolvedValue({ status: 'completed' }),
        after_perform: jest.fn().mockResolvedValue(undefined),
        on_failure: jest.fn().mockResolvedValue(undefined),
        close: jest.fn().mockResolvedValue(undefined),
        setAttemptsMade: jest.fn(),
        setMaxAttempts: jest.fn(),
      };
      jobClasses.SetJobDescriptionJob.mockImplementation(() => mockJobInstance);

      const result = await jobProcessor(mockJob);

      expect(mockJobInstance.before_perform).toHaveBeenCalledWith({ vacancy_id: 1 });
      expect(mockJobInstance.perform).toHaveBeenCalledWith({ vacancy_id: 1 });
      expect(mockJobInstance.after_perform).toHaveBeenCalledWith(
        { vacancy_id: 1 },
        { status: 'completed' },
      );
      expect(mockJobInstance.close).toHaveBeenCalled();
      expect(result).toEqual({ status: 'completed' });
    });

    it('should throw error for unknown job types', async () => {
      mockJob.name = 'UnknownJob';

      await expect(jobProcessor(mockJob)).rejects.toThrow('Unknown job type: UnknownJob');
    });

    it('should handle job failures', async () => {
      const error = new Error('Job failed');
      const mockJobInstance = {
        before_perform: jest.fn().mockResolvedValue(undefined),
        perform: jest.fn().mockRejectedValue(error),
        on_failure: jest.fn().mockResolvedValue(undefined),
        close: jest.fn().mockResolvedValue(undefined),
        setAttemptsMade: jest.fn(),
        setMaxAttempts: jest.fn(),
      };
      jobClasses.SetJobDescriptionJob.mockImplementation(() => mockJobInstance);

      await expect(jobProcessor(mockJob)).rejects.toThrow('Job failed');

      expect(mockJobInstance.on_failure).toHaveBeenCalledWith({ vacancy_id: 1 }, error);
      expect(mockJobInstance.close).toHaveBeenCalled();
    });

    it('should clean up job instance without close method', async () => {
      const mockJobInstance = {
        before_perform: jest.fn().mockResolvedValue(undefined),
        perform: jest.fn().mockResolvedValue({ status: 'completed' }),
        after_perform: jest.fn().mockResolvedValue(undefined),
        on_failure: jest.fn().mockResolvedValue(undefined),
        setAttemptsMade: jest.fn(),
        setMaxAttempts: jest.fn(),
        // No close method
      };
      jobClasses.SetJobDescriptionJob.mockImplementation(() => mockJobInstance);

      const result = await jobProcessor(mockJob);

      expect(result).toEqual({ status: 'completed' });
      // Should not throw error when close method doesn't exist
    });
  });

  describe('event handlers', () => {
    beforeEach(async () => {
      await worker.start();
    });

    it('should handle completed jobs', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const completedHandler = mockBullWorker.on.mock.calls.find(
        call => call[0] === 'completed',
      )[1];

      const mockJob = {
        name: 'TestJob',
        id: '123',
        returnvalue: { status: 'completed' },
      };

      completedHandler(mockJob);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Job TestJob (123) completed successfully'),
      );
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Result:'), {
        status: 'completed',
      });

      consoleSpy.mockRestore();
    });

    it('should handle failed jobs', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const failedHandler = mockBullWorker.on.mock.calls.find(call => call[0] === 'failed')[1];

      const mockJob = { name: 'TestJob', id: '123' };
      const error = new Error('Test error');

      failedHandler(mockJob, error);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Job TestJob (123) failed:'),
        'Test error',
      );

      consoleSpy.mockRestore();
    });

    it('should handle worker errors', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const errorHandler = mockBullWorker.on.mock.calls.find(call => call[0] === 'error')[1];

      const error = new Error('Worker error');

      errorHandler(error);

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Worker error:'), error);

      consoleSpy.mockRestore();
    });

    it('should handle stalled jobs', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      const stalledHandler = mockBullWorker.on.mock.calls.find(call => call[0] === 'stalled')[1];

      stalledHandler('job-123');

      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Job job-123 stalled'));

      consoleSpy.mockRestore();
    });

    it('should handle job progress', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const progressHandler = mockBullWorker.on.mock.calls.find(call => call[0] === 'progress')[1];

      const mockJob = { name: 'TestJob', id: '123' };

      progressHandler(mockJob, 50);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Job TestJob (123) progress: 50%'),
      );

      consoleSpy.mockRestore();
    });
  });

  describe('getStats', () => {
    it('should return null when worker is not started', async () => {
      const stats = await worker.getStats();
      expect(stats).toBeNull();
    });

    it('should return worker statistics when started', async () => {
      await worker.start();

      const stats = await worker.getStats();

      expect(stats).toEqual(
        expect.objectContaining({
          isRunning: true,
          concurrency: 10,
          registeredJobs: expect.arrayContaining([
            'SetJobDescriptionJob',
            'SetVacancyGroupVariablesJob',
          ]),
          processedJobs: expect.any(Number),
          failedJobs: expect.any(Number),
          workerId: expect.any(String),
          uptime: expect.any(Number),
          uptimeFormatted: expect.any(String),
          startTime: expect.any(String),
          hostname: expect.any(String),
          pid: expect.any(Number),
          nodeVersion: expect.any(String),
          memoryUsage: expect.any(Object),
        }),
      );
    });

    it('should return correct running status when worker is closing', async () => {
      await worker.start();
      mockBullWorker.closing = true;

      const stats = await worker.getStats();

      expect(stats.isRunning).toBe(false);
    });
  });

  describe('graceful shutdown', () => {
    it('should handle shutdown gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const processExitSpy = jest.spyOn(process, 'exit').mockImplementation();

      await worker.start();

      // Simulate shutdown by directly calling the shutdown logic
      worker.isShuttingDown = true;

      // Mock the worker and redis to exist
      worker.worker = mockBullWorker;
      worker.redis = mockRedis;

      // Simulate the shutdown process
      await mockBullWorker.close();
      await closeRedisConnection();

      expect(mockBullWorker.close).toHaveBeenCalled();
      expect(closeRedisConnection).toHaveBeenCalled();

      consoleSpy.mockRestore();
      processExitSpy.mockRestore();
    });
  });
});
