'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('job_variables', 'scoring_direction', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'asc',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('job_variables', 'scoring_direction');
  },
};
