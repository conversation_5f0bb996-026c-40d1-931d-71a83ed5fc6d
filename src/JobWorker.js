const { Worker } = require('bullmq');
const { getRedisConnection, closeRedisConnection } = require('./config/redis');
const config = require('./config/config');
const os = require('os');
const crypto = require('crypto');
const WorkerHealthServer = require('./WorkerHealthServer');
const {
  recordJobStart,
  recordJobCompletion,
  recordJobRetry,
  updateWorkerStats,
  updateQueueStats,
} = require('./middlewares/worker-prometheus');
const { sequelize } = require('./config/sequelize');

// Import all job classes from the jobs index
const jobClasses = require('./jobs');

class JobWorker {
  constructor() {
    this.redis = getRedisConnection();
    this.worker = null;
    this.isShuttingDown = false;

    // Generate unique worker ID for cluster identification
    this.workerId = this.generateWorkerId();
    this.startTime = new Date();
    this.processedJobs = 0;
    this.failedJobs = 0;

    // Use job classes from the jobs index
    this.jobClasses = jobClasses;

    // Health check server for Kubernetes probes
    this.healthServer = null;

    // Setup database metrics tracking
    this.setupDatabaseMetrics();
  }

  /**
   * Setup database metrics tracking for the worker
   */
  setupDatabaseMetrics() {
    const { recordDbCall } = require('./middlewares/worker-prometheus');

    // Track Sequelize queries
    sequelize.addHook('beforeQuery', options => {
      options._metricsStartTime = Date.now();
    });

    sequelize.addHook('afterQuery', options => {
      if (options._metricsStartTime) {
        const duration = (Date.now() - options._metricsStartTime) / 1000;
        const currentJob = options._currentJob || 'unknown';
        recordDbCall('postgresql', duration, currentJob);
      }
    });

    console.log('📊 Database metrics tracking enabled for worker');
  }

  /**
   * Generate a unique worker ID for cluster identification
   * @returns {string} Unique worker identifier
   */
  generateWorkerId() {
    const hostname = os.hostname();
    const pid = process.pid;
    const random = crypto.randomBytes(4).toString('hex');
    return `${hostname}-${pid}-${random}`;
  }

  /**
   * Start the worker
   */
  async start() {
    console.log(`🚀 Starting BullMQ worker [${this.workerId}]...`);
    console.log(`📊 Worker configuration:`);
    console.log(`   - Concurrency: ${config.queue.concurrency}`);
    console.log(`   - Hostname: ${os.hostname()}`);
    console.log(`   - PID: ${process.pid}`);
    console.log(`   - Node version: ${process.version}`);
    console.log(`   - Environment: ${config.nodeEnv}`);
    console.log(`   - Queue name: ${config.queue.name}`);

    this.worker = new Worker(
      config.queue.name,
      async job => {
        const { name, data } = job;
        const jobStartTime = Date.now();
        const attemptsMade = job.attemptsMade || 1;
        const maxAttempts = job.opts?.attempts || 1;

        console.log(
          `[${this.workerId}] Processing job: ${name} (ID: ${job.id}, Attempt: ${attemptsMade}/${maxAttempts}) with data:`,
          data,
        );

        // Record job start for Prometheus metrics
        recordJobStart(name, this.workerId);

        // Find the job class by name
        const JobClass = this.jobClasses[name];
        if (!JobClass) {
          this.failedJobs++;
          const duration = (Date.now() - jobStartTime) / 1000;
          recordJobCompletion(name, this.workerId, duration, false, 'unknown_job_type');
          throw new Error(`Unknown job type: ${name}`);
        }

        const instance = new JobClass();
        instance.setAttemptsMade(attemptsMade);
        instance.setMaxAttempts(maxAttempts);

        // Set job context for database metrics
        sequelize.addHook('beforeQuery', options => {
          if (!options._metricsStartTime) {
            options._metricsStartTime = Date.now();
          }
          options._currentJob = name;
        });

        try {
          await instance.before_perform(data);
          const result = await instance.perform(data);
          await instance.after_perform(data, result);

          this.processedJobs++;
          const duration = (Date.now() - jobStartTime) / 1000;
          console.log(
            `[${this.workerId}] ✅ Job ${name} (${job.id}) completed in ${duration * 1000}ms`,
          );

          // Record successful job completion
          recordJobCompletion(name, this.workerId, duration, true);

          return result;
        } catch (error) {
          this.failedJobs++;
          const duration = (Date.now() - jobStartTime) / 1000;
          const willRetry = attemptsMade < maxAttempts;
          const retryInfo = willRetry
            ? `(will retry, attempt ${attemptsMade + 1}/${maxAttempts})`
            : '(no more retries)';

          console.error(
            `[${this.workerId}] ❌ Job ${name} (${job.id}) failed: ${error.message} ${retryInfo}`,
          );

          // Record retry if will retry
          if (willRetry) {
            recordJobRetry(name, this.workerId);
          }

          // Check if job should be retried based on custom logic
          if (typeof instance.shouldRetry === 'function') {
            const shouldRetryCustom = instance.shouldRetry(error, attemptsMade, maxAttempts);
            if (!shouldRetryCustom && willRetry) {
              console.log(`[${this.workerId}] 🚫 Custom retry logic says not to retry this job`);
            }
          }

          await instance.on_failure(data, error);

          // Record failed job completion with error type
          const errorType = error.name || 'unknown_error';
          recordJobCompletion(name, this.workerId, duration, false, errorType);

          throw error;
        } finally {
          // Clean up job instance resources
          if (typeof instance.close === 'function') {
            await instance.close();
          }
        }
      },
      {
        connection: this.redis,
        concurrency: config.queue.concurrency,
        removeOnComplete: config.queue.defaultJobOptions.removeOnComplete,
        removeOnFail: config.queue.defaultJobOptions.removeOnFail,
        // Add cluster-safe settings
        stalledInterval: config.queue.stalledInterval,
        maxStalledCount: config.queue.maxStalledCount,
      },
    );

    // Worker event handlers with cluster identification
    this.worker.on('completed', job => {
      console.log(`[${this.workerId}] ✅ Job ${job.name} (${job.id}) completed successfully`);
      if (job.returnvalue && process.env.NODE_ENV !== 'production') {
        console.log(`[${this.workerId}] Result:`, job.returnvalue);
      }
    });

    this.worker.on('failed', (job, err) => {
      console.error(`[${this.workerId}] ❌ Job ${job.name} (${job.id}) failed:`, err.message);
      if (process.env.NODE_ENV !== 'production') {
        console.error(`[${this.workerId}] Stack trace:`, err.stack);
      }
    });

    this.worker.on('error', err => {
      console.error(`[${this.workerId}] 🚨 Worker error:`, err);
    });

    this.worker.on('stalled', jobId => {
      console.warn(
        `[${this.workerId}] ⚠️  Job ${jobId} stalled - will be retried by another worker`,
      );
    });

    this.worker.on('progress', (job, progress) => {
      console.log(`[${this.workerId}] 📊 Job ${job.name} (${job.id}) progress: ${progress}%`);
    });

    // Setup graceful shutdown
    this.setupGracefulShutdown();

    // Start health check server for Kubernetes probes (if enabled)
    if (config.worker.enableHealthServer) {
      console.log('🏥 Starting health check server for Kubernetes probes...');
      await this.startHealthServer();
    }

    // Start periodic metrics updates
    this.startMetricsUpdater();

    console.log(`🚀 Worker [${this.workerId}] started successfully`);
    console.log(`📊 Configuration: concurrency=${config.queue.concurrency}, stalledInterval=30s`);
    console.log('📋 Registered job types:', Object.keys(this.jobClasses));
    console.log('⏳ Waiting for jobs...');
  }

  /**
   * Setup graceful shutdown handlers
   */
  setupGracefulShutdown() {
    let forceExitTimer = null;

    const forceExit = () => {
      console.log('� Force exiting immediately...');
      process.exit(1);
    };

    const shutdown = async signal => {
      // If already shutting down, force exit on second signal
      if (this.isShuttingDown) {
        console.log('� Shutdown already in progress, forcing exit...');
        forceExit();
        return;
      }

      this.isShuttingDown = true;
      console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

      // Set a hard limit - force exit after 8 seconds no matter what
      forceExitTimer = setTimeout(forceExit, 8000);

      try {
        // Stop the worker first (most important)
        if (this.worker) {
          console.log('⏹️  Stopping worker...');
          await Promise.race([
            this.worker.close(),
            new Promise(resolve => setTimeout(resolve, 5000)), // 5 second timeout
          ]);
          console.log('✅ Worker stopped successfully');
        }

        // Stop metrics updater
        console.log('📊 Stopping metrics updater...');
        this.stopMetricsUpdater();

        // Stop health server
        if (this.healthServer) {
          console.log('🏥 Stopping health server...');
          await Promise.race([
            this.stopHealthServer(),
            new Promise(resolve => setTimeout(resolve, 2000)), // 2 second timeout
          ]);
        }

        // Close Redis connection
        try {
          await Promise.race([
            closeRedisConnection(),
            new Promise(resolve => setTimeout(resolve, 2000)), // 2 second timeout
          ]);
          console.log('🔌 Redis connection closed');
        } catch (error) {
          console.log('⚠️  Redis close error (ignoring):', error.message);
        }

        console.log('👋 Graceful shutdown completed');

        // Clear the force exit timer
        if (forceExitTimer) {
          clearTimeout(forceExitTimer);
        }

        // Exit cleanly
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        if (forceExitTimer) {
          clearTimeout(forceExitTimer);
        }
        process.exit(1);
      }
    };

    // Handle different shutdown signals with simpler handlers
    const handleShutdown = signal => {
      // Don't await the shutdown - let it run async to avoid blocking
      shutdown(signal).catch(error => {
        console.error('❌ Shutdown error:', error);
        process.exit(1);
      });
    };

    process.on('SIGTERM', () => handleShutdown('SIGTERM'));
    process.on('SIGINT', () => handleShutdown('SIGINT'));
    process.on('SIGUSR2', () => handleShutdown('SIGUSR2')); // For nodemon

    // Handle uncaught exceptions
    process.on('uncaughtException', error => {
      console.error('🚨 Uncaught Exception:', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
  }

  /**
   * Get worker statistics
   */
  async getStats() {
    if (!this.worker) {
      return null;
    }

    const uptime = Date.now() - this.startTime.getTime();

    return {
      workerId: this.workerId,
      isRunning: !this.worker.closing,
      concurrency: config.queue.concurrency,
      registeredJobs: Object.keys(this.jobClasses),
      processedJobs: this.processedJobs,
      failedJobs: this.failedJobs,
      uptime,
      uptimeFormatted: this.formatUptime(uptime),
      startTime: this.startTime.toISOString(),
      hostname: os.hostname(),
      pid: process.pid,
      nodeVersion: process.version,
      memoryUsage: process.memoryUsage(),
    };
  }

  /**
   * Format uptime in human readable format
   * @param {number} uptime - Uptime in milliseconds
   * @returns {string} Formatted uptime
   */
  formatUptime(uptime) {
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  /**
   * Health check for Kubernetes probes
   * @returns {Object} Health status
   */
  async healthCheck() {
    const isHealthy = this.worker && !this.worker.closing && !this.isShuttingDown;

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      workerId: this.workerId,
      uptime: Date.now() - this.startTime.getTime(),
      processedJobs: this.processedJobs,
      failedJobs: this.failedJobs,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Start periodic metrics updates for Prometheus
   */
  startMetricsUpdater() {
    if (process.env.NODE_ENV === 'test') {
      return; // Don't start metrics updater in tests
    }

    // Update metrics every 10 seconds
    this.metricsInterval = setInterval(async () => {
      try {
        const uptime = (Date.now() - this.startTime.getTime()) / 1000;
        const memoryUsage = process.memoryUsage();

        // Update worker stats
        updateWorkerStats(this.workerId, uptime, config.queue.concurrency, memoryUsage);

        // Update queue stats if we can access the queue
        if (this.worker && this.worker.opts && this.worker.opts.connection) {
          try {
            // Try to get queue stats from Redis
            const Queue = require('bullmq').Queue;
            const queue = new Queue('default', { connection: this.worker.opts.connection });

            const waiting = await queue.getWaiting();
            updateQueueStats('default', waiting.length);
          } catch (error) {
            // Silently ignore queue stats errors to avoid noise in logs
            console.debug('Queue stats error (ignoring):', error.message);
          }
        }
      } catch (error) {
        console.error(`[${this.workerId}] ❌ Error updating metrics:`, error);
      }
    }, 10000);

    console.log(`📊 Started Prometheus metrics updater for worker [${this.workerId}]`);
  }

  /**
   * Stop periodic metrics updates
   */
  stopMetricsUpdater() {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = null;
    }
  }

  /**
   * Start the health check server
   */
  async startHealthServer() {
    this.healthServer = new WorkerHealthServer(this, config.worker.healthPort);

    try {
      await this.healthServer.start();
    } catch (error) {
      console.error('❌ Failed to start health check server:', error);
      // Don't fail the worker startup if health server fails
    }
  }

  /**
   * Stop the health check server
   */
  async stopHealthServer() {
    if (this.healthServer) {
      try {
        await this.healthServer.stop();
      } catch (error) {
        console.error('❌ Error stopping health check server:', error);
      }
    }
  }
}

// Start the worker if this file is run directly
if (require.main === module) {
  const worker = new JobWorker();

  worker.start().catch(error => {
    console.error('❌ Failed to start worker:', error);
    process.exitCode = 1;
  });
}

module.exports = JobWorker;
