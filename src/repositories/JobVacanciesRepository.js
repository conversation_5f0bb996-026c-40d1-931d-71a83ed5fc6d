const { Op } = require('sequelize');
const BaseRepository = require('./BaseRepository');
const { JobVacancy } = require('../models');

class JobVacanciesRepository extends BaseRepository {
  constructor() {
    super(JobVacancy);
  }

  includeJobLevel() {
    return {
      association: 'jobLevel',
      attributes: ['id', 'name', 'order_level'],
    };
  }

  includeBone() {
    return {
      association: 'bone',
      attributes: ['id', 'name'],
    };
  }

  includeWorkArea() {
    return {
      association: 'workArea',
      attributes: ['id', 'name'],
    };
  }

  includeWorkAreas() {
    return {
      association: 'workAreas',
      attributes: ['id', 'name'],
    };
  }

  includeJobTitle() {
    return {
      association: 'jobTitle',
      attributes: ['id', 'name'],
    };
  }

  /**
   * Filter job vacancies by ID
   * Usage: /api/v1/job_vacancies?id=1
   * @param {number} id - Job vacancy ID
   * @returns {Object} Where condition for ID filter
   */
  filterById(id) {
    return { id };
  }

  /**
   * Filter job vacancies by search term in name
   * Usage: /api/v1/job_vacancies?search=developer
   * @param {string} search - Search term
   * @returns {Object} Where condition for search filter
   */
  filterBySearch(search) {
    if (!search) return null;

    return {
      name: {
        [Op.iLike]: `%${search}%`,
      },
    };
  }

  /**
   * Filter job vacancies by status
   * Usage: /api/v1/job_vacancies?status=active
   * @param {string} status - Status to filter by
   * @returns {Object} Where condition for status filter
   */
  filterByStatus(status) {
    if (!status) return null;
    return { status };
  }
}

module.exports = JobVacanciesRepository;
