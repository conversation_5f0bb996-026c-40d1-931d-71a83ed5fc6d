const JobVacancyFactory = require('../factories/JobVacancyFactory');
const JobTitleFactory = require('../factories/JobTitleFactory');
const SetJobDescriptionJob = require('../../src/jobs/SetJobDescriptionJob');
const SetVacancyGroupVariablesJob = require('../../src/jobs/SetVacancyGroupVariablesJob');

// Mock GenerateJobDescService at the top level
const generatedJobDescResponse = {
  jobDescription: {
    title: 'Software Engineer',
    key_responsibilities: [
      'Design and develop scalable web applications',
      'Write clean, maintainable, and efficient code',
      'Collaborate with cross-functional teams to define and implement features',
      'Participate in code reviews and maintain coding standards',
      'Debug and troubleshoot application issues',
    ],
    summary:
      'We are seeking a talented Software Engineer to join our development team. You will be responsible for designing, developing, and maintaining software applications that meet our business requirements and provide excellent user experiences.',
    requirements: [
      "Bachelor's degree in Computer Science or related field",
      '3+ years of experience in software development',
    ],
    qualifications: [
      'Experience with cloud platforms (AWS, Azure, or GCP)',
      'Knowledge of containerization technologies (Docker, Kubernetes)',
      'Familiarity with CI/CD pipelines and DevOps practices',
      'Understanding of software testing methodologies',
      'Excellent communication and teamwork skills',
    ],
    skills: [
      'Proficiency in JavaScript, Python, or Java',
      'Experience with front-end frameworks (React, Angular, or Vue.js)',
      'Strong problem-solving and analytical skills',
    ],
  },
  onetsocCodes: ['15-1256.00', '15-1252.00', '15-1243.00'],
};

describe('SetJobDescriptionJob', () => {
  describe('Successful Job Execution', () => {
    it('should process a basic vacancy successfully', async () => {
      const vacancy = await JobVacancyFactory.create({
        name: 'Software Engineer',
        status: 'generating_jobdesc',
      });

      const job = new SetJobDescriptionJob();
      job.generateJobDescService.generateJobDesc = jest
        .fn()
        .mockResolvedValue(generatedJobDescResponse);

      await job.perform({ vacancy_id: vacancy.id });

      // Verify database was updated
      await vacancy.reload();
      expect(vacancy.job_desc).toEqual(
        generatedJobDescResponse.jobDescription.key_responsibilities,
      );
      expect(vacancy.related_onetsoc_codes).toEqual(generatedJobDescResponse.onetsocCodes);
      expect(vacancy.detailed_descriptions).toEqual(generatedJobDescResponse.jobDescription);
      expect(vacancy.status).toBe('generating_job_variables');

      // expect SetVacancyGroupVariablesJob to have been enqueued
      expect(global.performAsyncSpy.mock.instances[0]).toBe(SetVacancyGroupVariablesJob);
      expect(global.performAsyncSpy).toHaveBeenCalledWith({
        vacancy_id: vacancy.id,
      });
    });

    it('should merge detailed descriptions correctly', async () => {
      const existingDescriptions = {
        work_inputs: ['Existing work input 1', 'Existing work input 2'],
        work_outputs: ['Existing work output 1', 'Existing work output 2'],
      };

      const jobTitle = await JobTitleFactory.create();
      expect(jobTitle.prefilled_details.work_inputs).toBeUndefined();
      expect(jobTitle.prefilled_details.work_outputs).toBeUndefined();

      const vacancy = await JobVacancyFactory.create({
        name: 'Software Engineer',
        status: 'generating_jobdesc',
        detailed_descriptions: existingDescriptions,
        job_title_id: jobTitle.id,
      });

      const job = new SetJobDescriptionJob();
      job.generateJobDescService.generateJobDesc = jest
        .fn()
        .mockResolvedValue(generatedJobDescResponse);

      await job.perform({ vacancy_id: vacancy.id });

      // Verify database was updated
      await vacancy.reload();
      expect(vacancy.detailed_descriptions).toEqual({
        ...existingDescriptions,
        ...generatedJobDescResponse.jobDescription,
      });

      await jobTitle.reload();
      expect(jobTitle.prefilled_details.work_inputs).toEqual([
        'Existing work input 1',
        'Existing work input 2',
      ]);
      expect(jobTitle.prefilled_details.work_outputs).toEqual([
        'Existing work output 1',
        'Existing work output 2',
      ]);
    });
  });

  describe('Error Handling', () => {
    it('should handle generateJobDesc service error and set status to draft on last retry', async () => {
      const vacancy = await JobVacancyFactory.create({
        name: 'Software Engineer',
        status: 'generating_jobdesc',
      });

      // Mock the service method to throw an error
      const job = new SetJobDescriptionJob();
      job.setAttemptsMade(1);
      job.setMaxAttempts(3);
      job.generateJobDescService.generateJobDesc = jest
        .fn()
        .mockRejectedValue(new Error('Service error'));

      // Expect the job to throw the error
      await expect(job.perform({ vacancy_id: vacancy.id })).rejects.toThrow('Service error');

      // Verify database not updated to draft when first retry
      await vacancy.reload();
      expect(vacancy.status).toBe('generating_jobdesc');

      // assume already reached maxAttempts
      job.setAttemptsMade(3);
      await expect(job.perform({ vacancy_id: vacancy.id })).rejects.toThrow('Service error');

      // Verify database updated to draft when reached maxAttempts
      await vacancy.reload();
      expect(vacancy.status).toBe('draft');
    });
  });
});
