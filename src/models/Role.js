'use strict';
const AppModel = require('./AppModel');

class Role extends AppModel {
  static associate(models) {
    this.hasMany(models.RolePermission, {
      foreignKey: 'role_id',
      as: 'rolePermissions',
    });
    this.belongsToMany(models.Permission, {
      through: models.RolePermission,
      foreignKey: 'role_id',
      otherKey: 'permission_id',
      as: 'permissions',
    });
  }
}

module.exports = Role;
