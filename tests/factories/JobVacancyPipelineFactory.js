'use strict';

const BaseFactory = require('./BaseFactory');
const { JobVacancyPipeline } = require('../../src/models');

class JobVacancyPipelineFactory extends BaseFactory {
  constructor() {
    super(JobVacancyPipeline);
  }

  static defaultAttributes() {
    const pipelineNames = [
      'Initial Screening',
      'Technical Interview',
      'HR Interview',
      'Final Interview',
      'Offer Extended',
      'Hired',
    ];

    const name = this.faker.helpers.arrayElement(pipelineNames);
    const parameterizedName = name.toLowerCase().replace(/\s+/g, '_');

    return {
      name,
      parameterized_name: parameterizedName,
      job_vacancy_id: 1, // Will be overridden in tests
      order_level: this.faker.number.int({ min: 1, max: 10 }),
    };
  }

  static traits() {
    return {
      screening: {
        name: 'Initial Screening',
        parameterized_name: 'initial_screening',
        order_level: 1,
      },
      technical: {
        name: 'Technical Interview',
        parameterized_name: 'technical_interview',
        order_level: 2,
      },
      hr_interview: {
        name: 'HR Interview',
        parameterized_name: 'hr_interview',
        order_level: 3,
      },
      final: {
        name: 'Final Interview',
        parameterized_name: 'final_interview',
        order_level: 4,
      },
      offer: {
        name: 'Offer Extended',
        parameterized_name: 'offer_extended',
        order_level: 5,
      },
      hired: {
        name: 'Hired',
        parameterized_name: 'hired',
        order_level: 6,
      },
    };
  }
}

module.exports = JobVacancyPipelineFactory;
