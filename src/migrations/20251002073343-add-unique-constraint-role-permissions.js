'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, _Sequelize) {
    await queryInterface.addIndex('role_permissions', ['role_id', 'permission_id'], {
      unique: true,
      name: 'role_permissions_role_id_permission_id_unique_constraint',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeIndex(
      'role_permissions',
      'role_permissions_role_id_permission_id_unique_constraint',
    );
  },
};
