const express = require('express');
const authController = require('../controllers/AuthController');
const { authenticateToken, requirePermission } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route GET /api/v1/auth/
 * @desc Inspect user role and permissions
 * @access Private
 */
router.get('/', authenticateToken, requirePermission('*'), authController.index);

/**
 * @route POST /api/v1/auth/login
 * @desc Authenticate user and return JWT token
 * @access Public
 */
router.post('/login', authController.login);

module.exports = router;
