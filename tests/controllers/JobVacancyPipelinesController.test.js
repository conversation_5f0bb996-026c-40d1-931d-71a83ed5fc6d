const api = require('../utils/requestHelper');

const {
  UserFactory,
  JobTitleFactory,
  JobVacancyFactory,
  JobVacancyPipelineFactory,
} = require('../factories');

describe('JobVacancyPipelinesController', () => {
  let admin;
  let user;
  let jobTitle;
  let jobVacancy1;
  let jobVacancy2;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();

    // Create job titles and vacancies
    jobTitle = await JobTitleFactory.create();
    jobVacancy1 = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
    jobVacancy2 = await JobVacancyFactory.create({ job_title_id: jobTitle.id });

    // Create pipelines for jobVacancy1
    await JobVacancyPipelineFactory.createWithTrait('screening', {
      job_vacancy_id: jobVacancy1.id,
    });
    await JobVacancyPipelineFactory.createWithTrait('technical', {
      job_vacancy_id: jobVacancy1.id,
    });
    await JobVacancyPipelineFactory.createWithTrait('final', {
      job_vacancy_id: jobVacancy1.id,
    });
  });

  describe('GET /api/v1/job_vacancies/:id/pipelines', () => {
    describe('successful requests', () => {
      it('should return pipelines for a job vacancy', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);

        // Should be sorted by order_level (ascending)
        expect(response.body.data[0].order_level).toBeLessThanOrEqual(
          response.body.data[1].order_level,
        );
        expect(response.body.data[1].order_level).toBeLessThanOrEqual(
          response.body.data[2].order_level,
        );

        // Check data structure
        const firstPipeline = response.body.data[0];
        expect(firstPipeline).toHaveProperty('id');
        expect(firstPipeline).toHaveProperty('name');
        expect(firstPipeline).toHaveProperty('parameterized_name');
        expect(firstPipeline).toHaveProperty('order_level');
        expect(firstPipeline).toHaveProperty('job_vacancy_id', jobVacancy1.id);
      });

      it('should return empty array for job vacancy with no pipelines', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy2.id}/pipelines`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(0);
      });

      it('should support pagination', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`, {
            page: 1,
            limit: 2,
          });

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.pagination).toHaveProperty('page', 1);
        expect(response.body.pagination).toHaveProperty('limit', 2);
        expect(response.body.pagination).toHaveProperty('total', 3);
      });

      it('should support custom sorting', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`, {
            sort: 'name',
            sort_direction: 'desc',
          });

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);

        // Should be sorted by name descending
        const names = response.body.data.map(p => p.name);
        const sortedNames = [...names].sort().reverse();
        expect(names).toEqual(sortedNames);
      });

      it('should default to order_level ascending sort', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`);

        expect(response.status).toBe(200);

        const orderLevels = response.body.data.map(p => p.order_level);
        const sortedOrderLevels = [...orderLevels].sort((a, b) => a - b);
        expect(orderLevels).toEqual(sortedOrderLevels);
      });
    });

    describe('validation errors', () => {
      it('should return 400 for invalid job_vacancy_id in URL', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies/invalid/pipelines');

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for invalid sort field', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`, {
            sort: 'invalid_field',
          });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for invalid sort direction', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`, {
            sort_direction: 'invalid',
          });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for invalid page number', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`, {
            page: 0,
          });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 for invalid limit', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`, {
            limit: 101,
          });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('business logic errors', () => {
      it('should return 404 for non-existent job vacancy', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies/99999/pipelines');

        expect(response.status).toBe(404);
        expect(response.body).toHaveProperty('error');
      });
    });

    describe('authentication and authorization', () => {
      it('should return 401 if not authenticated', async () => {
        const response = await api.get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`);

        expect(response.status).toBe(401);
      });

      it('should return 403 if user lacks permission', async () => {
        const response = await api
          .as(user)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`);

        expect(response.status).toBe(403);
      });
    });

    describe('edge cases', () => {
      it('should handle large page numbers gracefully', async () => {
        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`, {
            page: 999,
          });

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.page).toBe(999);
      });

      it('should handle job vacancy with many pipelines', async () => {
        // Create additional pipelines
        for (let i = 4; i <= 10; i++) {
          await JobVacancyPipelineFactory.create({
            job_vacancy_id: jobVacancy1.id,
            name: `Pipeline ${i}`,
            parameterized_name: `pipeline_${i}`,
            order_level: i,
          });
        }

        const response = await api
          .as(admin)
          .get(`/api/v1/job_vacancies/${jobVacancy1.id}/pipelines`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(10); // 3 original + 7 new
        expect(response.body.pagination.total).toBe(10);
      });
    });
  });
});
