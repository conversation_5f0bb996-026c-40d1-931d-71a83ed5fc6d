const express = require('express');
const { requirePermission } = require('../middlewares/auth');

/**
 * Creates a standard RESTful resource router with optional methods. By default, all methods are included.
 * @param {string} resourceName - The name of the resource (e.g., 'job_vacancies').
 * @param {object} controller - The controller object with index, create, show, update, destroy methods.
 * @param {object} options - Optional configuration object.
 * @param {string[]} options.only - Array of methods to include in the router.
 * @returns {express.Router}
 */
function resources(resourceName, controller, options = {}) {
  const router = express.Router({ mergeParams: true });

  const defaultMethods = ['index', 'create', 'show', 'update', 'destroy'];
  const methods = options.only || defaultMethods;

  for (const method of methods) {
    if (typeof controller[method] !== 'function') {
      throw new Error(
        `Controller for resource "${resourceName}" is missing required method "${method}".`,
      );
    }
  }

  if (methods.includes('index')) {
    router.get('/', requirePermission(`${resourceName}:index`), controller.index);
  }
  if (methods.includes('create')) {
    router.post('/', requirePermission(`${resourceName}:create`), controller.create);
  }
  if (methods.includes('show')) {
    router.get('/:id', requirePermission(`${resourceName}:show`), controller.show);
  }
  if (methods.includes('update')) {
    router.patch('/:id', requirePermission(`${resourceName}:update`), controller.update);
  }
  if (methods.includes('destroy')) {
    router.delete('/:id', requirePermission(`${resourceName}:destroy`), controller.destroy);
  }

  return router;
}

module.exports = resources;
