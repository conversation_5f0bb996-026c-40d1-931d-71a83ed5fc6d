const ApplicationJob = require('./ApplicationJob');
const OnetService = require('../services/OnetService');
const GoogleAiService = require('../services/external/GoogleAiService');
const GenerateVgvService = require('../services/JobVacancy/GenerateVgvService');
const {
  JobVacancy,
  VacancyGroupVariable,
  JobVacancyVariableBenchmark,
  transaction,
} = require('../models');

class SetVacancyGroupVariablesJob extends ApplicationJob {
  constructor() {
    super();
    this.generateVgvService = new GenerateVgvService({
      googleAiService: new GoogleAiService(),
      onetService: new OnetService(),
    });
  }

  async perform(data) {
    const { vacancy_id: jobVacancyId } = data;
    const vacancy = await JobVacancy.findByPk(jobVacancyId);
    if (!vacancy) throw new Error(`Job vacancy with id ${jobVacancyId} not found`);
    const isGenerateKsao = this.config.vacancyGroupVariable.generateKsao;

    try {
      let ksao = {};
      if (isGenerateKsao) ksao = await this.generateVgvService.generateKsao(vacancy);

      const { jvvbRecords, jgvAvgScales } = await this.generateJvvbRecords(vacancy);
      const vgvRecords = await this.generateVgvService.generateVgvRecords({
        ksao,
        jobVacancyId: vacancy.id,
        boneId: vacancy.bone_id,
        jgvAvgScales,
      });

      await transaction(async () => {
        await JobVacancyVariableBenchmark.destroy({
          where: { job_vacancy_id: jobVacancyId },
        });

        await VacancyGroupVariable.destroy({
          where: { job_vacancy_id: jobVacancyId },
        });

        if (jvvbRecords.length > 0) {
          await JobVacancyVariableBenchmark.bulkCreate(jvvbRecords, {
            updateOnDuplicate: [
              'baseline_scale',
              'baseline_score',
              'configured_baseline_scale',
              'configured_baseline_score',
              'updated_at',
              'deleted_at',
            ],
          });
        }

        if (vgvRecords.length > 0) {
          await VacancyGroupVariable.bulkCreate(vgvRecords, {
            updateOnDuplicate: [
              'keyword_match_count',
              'keyword_total_count',
              'match_type',
              'weight',
              'bone_value',
              'default_weight',
              'avg_baseline_scale',
              'configured_baseline_scale',
              'updated_at',
              'deleted_at',
            ],
          });
        }

        await vacancy.update({
          ksao,
          status: 'draft',
        });
      });
    } catch (error) {
      // update to draft only when reached retry maxAttempts
      if (!this.shouldRetry(error, this.attemptsMade, this.maxAttempts)) {
        await vacancy.update({ status: 'draft' });
      }

      throw error;
    }
  }

  async generateJvvbRecords(vacancy) {
    const sql = `
      WITH user_variable_scores AS (
        SELECT u.id AS user_id,
          job_variables.id AS job_variable_id,
          jgv.id AS job_group_variable_id,
          job_variables.variable_type,
          CASE
            WHEN job_variables.variable_type = 'numeric' AND ujv.raw_value ~ '^[-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?$'
              THEN ujv.raw_value::DOUBLE PRECISION
            WHEN job_variables.variable_type = 'categorical'
              THEN (
                ujv.normalized_value::INTEGER / 7
              )::DOUBLE PRECISION -- Exact match only
            ELSE NULL
          END AS score,
          ujv.normalized_value AS scale
        FROM users u
        CROSS JOIN job_variables
        JOIN job_group_variables jgv
          ON jgv.id = job_variables.job_group_variable_id
          AND jgv.deleted_at IS NULL
        LEFT JOIN user_job_variables ujv
          ON ujv.user_id = u.id
          AND ujv.job_variable_id = job_variables.id
          AND ujv.deleted_at IS NULL
        WHERE u.deleted_at IS NULL
          AND job_variables.deleted_at IS NULL
      ), benchmark_scores AS (
        SELECT job_variable_id,
          job_group_variable_id,
          ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP(ORDER BY score)) AS baseline_score,
          ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP(ORDER BY scale)) AS baseline_scale
        FROM user_variable_scores
        WHERE user_id IN (:user_ids)
          AND score IS NOT NULL
          AND score::DOUBLE PRECISION > 0
        GROUP BY job_variable_id, job_group_variable_id
      )

      SELECT jv.id AS job_variable_id,
        jv.job_group_variable_id,
        bs.baseline_score,
        bs.baseline_scale,
        jv.mandatory
      FROM job_variables jv
      LEFT JOIN benchmark_scores bs ON bs.job_variable_id = jv.id
      WHERE jv.deleted_at IS NULL;
    `;

    const result = await vacancy.sequelize.query(sql, {
      replacements: { user_ids: vacancy.related_user_ids },
      type: vacancy.sequelize.QueryTypes.SELECT,
    });

    const jvvbRecords = [];
    const jgvScales = {};
    const jgvAvgScales = {};
    const blankMandatoryJgvIds = new Set();

    result.forEach(record => {
      jgvScales[record.job_group_variable_id] ||= [];
      jgvScales[record.job_group_variable_id].push(record.baseline_scale);

      if (!record.baseline_score) {
        if (record.mandatory) blankMandatoryJgvIds.add(record.job_group_variable_id);
        return;
      }

      jvvbRecords.push({
        job_vacancy_id: vacancy.id,
        job_variable_id: record.job_variable_id,
        baseline_score: record.baseline_score,
        baseline_scale: record.baseline_scale,
        configured_baseline_score: record.baseline_score,
        configured_baseline_scale: record.baseline_scale,
        updated_at: new Date(),
        deleted_at: null,
      });
    });

    Object.keys(jgvScales).forEach(key => {
      const jgvId = parseInt(key, 10);
      const filteredScales = jgvScales[jgvId].filter(scale => scale !== null);
      const hasBlankMandatory = blankMandatoryJgvIds.has(jgvId);
      const hasNoScores = filteredScales.length === 0;

      if (hasNoScores || hasBlankMandatory) {
        jgvAvgScales[jgvId] = hasBlankMandatory ? null : 0;
        return;
      }

      jgvAvgScales[jgvId] =
        filteredScales.reduce((sum, scale) => sum + scale, 0) / filteredScales.length;
    });

    return { jvvbRecords, jgvAvgScales };
  }
}

module.exports = SetVacancyGroupVariablesJob;
