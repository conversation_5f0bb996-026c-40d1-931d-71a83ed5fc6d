const BaseRepository = require('./BaseRepository');
const { VacancyGroupVariable } = require('../models');

class VacancyGroupVariablesRepository extends BaseRepository {
  constructor() {
    super(VacancyGroupVariable);
  }

  includeJobVariables() {
    return {
      association: 'jobGroupVariable',
      attributes: ['id', 'name', 'description', 'order_level', 'keywords'],
      include: [
        {
          association: 'jobVariables',
          attributes: ['id', 'name', 'variable_type', 'filter_scales', 'mandatory'],
        },
      ],
    };
  }

  filterByJobVacancyId(jobVacancyId) {
    return { job_vacancy_id: jobVacancyId };
  }
}

module.exports = VacancyGroupVariablesRepository;
