const AppCache = require('./AppCache');
const { Role } = require('../models');

class RolePermissionCache extends AppCache {
  async getRolePermissions(roleName, refresh = false) {
    let rolePermissions = null;
    const cacheKey = `rolePermissions:${roleName}`;
    if (!refresh) rolePermissions = await this.cache.get(cacheKey);

    if (!rolePermissions) {
      rolePermissions = await this.fetchRolePermissions(roleName);
      await this.cache.set(cacheKey, rolePermissions);
    }

    return rolePermissions;
  }

  async fetchRolePermissions(roleName) {
    const role = await Role.findOne({
      where: { name: roleName },
      include: [
        {
          association: 'permissions',
          attributes: ['name'],
        },
      ],
      attributes: [],
    });

    if (!role) return [];
    return role.permissions.map(p => p.name);
  }
}

module.exports = RolePermissionCache;
