'use strict';
const AppModel = require('./AppModel');

class UserVacancyVariableScore extends AppModel {
  static associate(models) {
    this.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });
    this.belongsTo(models.JobVacancy, {
      foreignKey: 'job_vacancy_id',
      as: 'jobVacancy',
    });
    this.belongsTo(models.JobVariable, {
      foreignKey: 'job_variable_id',
      as: 'jobVariable',
    });
  }
}

module.exports = UserVacancyVariableScore;
