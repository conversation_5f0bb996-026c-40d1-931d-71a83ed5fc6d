const AppService = require('./AppService');
const {
  JobVacancy,
  JobTitle,
  User,
  VacancyGroupVariable,
  JobVacancyWorkArea,
  sequelize,
  transaction,
} = require('../models');
const JobVacanciesRepository = require('../repositories/JobVacanciesRepository');
const JobTitleService = require('./JobTitleService');

// Import job classes
const SetJobDescriptionJob = require('../jobs/SetJobDescriptionJob');
const SetVacancyGroupVariablesJob = require('../jobs/SetVacancyGroupVariablesJob');

class JobVacancyService extends AppService {
  constructor() {
    super();
    this.repository = new JobVacanciesRepository();
    this.jobTitleService = new JobTitleService();
  }

  /**
   * Get all job vacancies
   * @param {Object} params - Query params (page, limit, filters, etc.)
   * @returns {Object} Job vacancies array and pagination info
   */
  async findAll(params = {}) {
    params.includes = ['jobLevel', 'bone', 'workAreas', 'jobTitle'];
    params['sort'] = params.sort || 'id';
    params['sort_direction'] = params.sort_direction || 'desc';
    const { rows, pagination } = await this.repository.findAll(params);

    const jobVacancyIds = rows.map(row => row.id);
    const countsById = await this.getUjvCounts(jobVacancyIds);

    return { job_vacancies: rows, pagination, countsById };
  }

  /**
   * Find a job vacancy by ID
   * @param {number} id - Job vacancy ID
   * @returns {Object}
   * @throws {NotFoundError} If job vacancy is not found
   */
  async findById(id) {
    const repoParams = { id, includes: ['jobLevel', 'bone', 'workAreas', 'jobTitle'] };
    const vacancy = await this.repository.findOne(repoParams);
    this.exists(vacancy, 'Job vacancy not found');

    let referenceUsers = [];
    if (vacancy.related_user_ids) {
      referenceUsers = await User.findAll({
        where: { id: vacancy.related_user_ids },
        attributes: ['id', 'name'],
      });
    }

    return { vacancy, referenceUsers };
  }

  /**
   * Create a new job vacancy
   * @param {Object} data - Job vacancy data
   * @returns {Object} Created job vacancy
   * @throws {NotFoundError} If job title is not found
   */
  async create(data) {
    if (!data.name && !data.job_title_id) {
      this.assert(false, 'Name or job title is required');
    }

    // Fetch job title name if job_title_id is provided but name is not
    let jobTitleName = data.name;
    if (!jobTitleName && data.job_title_id) {
      const jobTitle = await JobTitle.findByPk(data.job_title_id);
      this.exists(jobTitle, 'Job title not found');
      jobTitleName = jobTitle.name;
    }

    const followupAction = data.followup_action;
    delete data.followup_action;

    const actionMapping = {
      generate_jobdesc: 'generating_jobdesc',
      generate_job_variables: 'generating_job_variables',
    };

    const workAreaIds = data.work_area_ids;
    delete data.work_area_ids;

    const vacancyData = {
      ...data,
      name: jobTitleName,
      status: actionMapping[followupAction] || 'draft',
    };

    let vacancy = null;
    await transaction(async () => {
      const jobTitle = await this.jobTitleService.upsertPrefilledDetails({
        id: vacancyData.job_title_id,
        name: vacancyData.name,
        prefilled_details: {
          job_level_id: vacancyData.job_level_id,
          role_summary: vacancyData.role_summary,
          key_responsibilities: vacancyData.detailed_descriptions?.key_responsibilities,
          qualifications: vacancyData.detailed_descriptions?.qualifications,
          competencies: vacancyData.detailed_descriptions?.competencies,
          success_metrics: vacancyData.detailed_descriptions?.success_metrics,
          work_inputs: vacancyData.detailed_descriptions?.work_inputs,
          work_outputs: vacancyData.detailed_descriptions?.work_outputs,
        },
      });

      vacancy = await JobVacancy.create({
        ...vacancyData,
        job_title_id: jobTitle.id,
      });

      await this.upsertVacancyWorkAreas(vacancy.id, workAreaIds);
    });

    if (followupAction === 'generate_jobdesc') {
      await SetJobDescriptionJob.perform_async({ vacancy_id: vacancy.id });
    } else if (followupAction === 'generate_job_variables') {
      await SetVacancyGroupVariablesJob.perform_async({ vacancy_id: vacancy.id });
    }

    return vacancy;
  }

  /**
   * Update a job vacancy
   * @param {number} id - Job vacancy ID
   * @param {Object} data - Job vacancy data
   * @returns {Object} Updated job vacancy
   * @throws {NotFoundError} If job vacancy is not found
   */
  async update(id, data) {
    const vacancy = await this.repository.findOne({ id });
    this.exists(vacancy, 'Job vacancy not found');

    let jobTitleName = data.name;
    if (!jobTitleName && data.job_title_id) {
      const jobTitle = await JobTitle.findByPk(data.job_title_id);
      this.exists(jobTitle, 'Job title not found');
      jobTitleName = jobTitle.name;
    }

    const followupAction = data.followup_action;
    delete data.followup_action;

    const workAreaIds = data.work_area_ids;
    delete data.work_area_ids;

    if (followupAction) {
      const isDraft = vacancy.status === 'draft';
      const isActive = vacancy.status === 'active';
      const errorMessage = `Cannot update this vacancy, current status: ${vacancy.status}`;
      this.assert(isDraft || isActive, errorMessage);
    }

    const actionMapping = {
      generate_jobdesc: 'generating_jobdesc',
      generate_job_variables: 'generating_job_variables',
    };

    const additionalData = {};
    additionalData.status = actionMapping[followupAction];
    additionalData.name = jobTitleName || vacancy.name;

    const vacancyData = {
      ...data,
      ...additionalData,
    };

    await transaction(async () => {
      await vacancy.update(vacancyData);
      await this.upsertVacancyWorkAreas(vacancy.id, workAreaIds);

      await this.jobTitleService.upsertPrefilledDetails({
        id: vacancy.job_title_id,
        name: vacancy.name,
        prefilled_details: {
          job_level_id: vacancy.job_level_id,
          role_summary: vacancy.role_summary,
          key_responsibilities: vacancy.detailed_descriptions?.key_responsibilities,
          qualifications: vacancy.detailed_descriptions?.qualifications,
          competencies: vacancy.detailed_descriptions?.competencies,
          success_metrics: vacancy.detailed_descriptions?.success_metrics,
          work_inputs: vacancy.detailed_descriptions?.work_inputs,
          work_outputs: vacancy.detailed_descriptions?.work_outputs,
        },
      });
    });

    if (followupAction === 'generate_jobdesc') {
      await SetJobDescriptionJob.perform_async({ vacancy_id: vacancy.id });
    } else if (followupAction === 'generate_job_variables') {
      await SetVacancyGroupVariablesJob.perform_async({ vacancy_id: vacancy.id });
    }

    return vacancy;
  }

  async delete(id) {
    const vacancy = await this.repository.findOne({ id });
    this.exists(vacancy, 'Job vacancy not found');

    await vacancy.destroy();
  }

  async averagingVarGroup(jobVacancyId) {
    const vgvList = await VacancyGroupVariable.findAll({
      where: { job_vacancy_id: jobVacancyId },
    });

    await Promise.all(
      vgvList.map(async vgv => {
        const sql = `
          WITH average_group_scores AS (
            SELECT ujvs.user_id,
              AVG(ujvs.match_score) AS avg_score
            FROM user_vacancy_variable_scores ujvs
            WHERE ujvs.job_vacancy_id = :job_vacancy_id
              AND ujvs.job_variable_id IN (
                SELECT id
                FROM job_variables
                WHERE job_group_variable_id = :job_group_variable_id
                  AND deleted_at IS NULL
              )
              AND ujvs.match_score IS NOT NULL
              AND ujvs.deleted_at IS NULL
            GROUP BY ujvs.user_id
          )

          INSERT INTO user_vacancy_group_variables (
            user_id,
            vacancy_group_variable_id,
            average_match_score,
            created_at,
            updated_at
          )

          SELECT u.id AS user_id,
            :vacancy_group_variable_id AS vacancy_group_variable_id,
            COALESCE(ags.avg_score, 0) AS average_match_score,
            NOW() AS created_at,
            NOW() AS updated_at
          FROM users u
          LEFT JOIN average_group_scores ags ON u.id = ags.user_id
          WHERE u.deleted_at IS NULL

          ON CONFLICT (user_id, vacancy_group_variable_id)
          DO UPDATE SET average_match_score = EXCLUDED.average_match_score,
            updated_at = EXCLUDED.updated_at;
        `;

        await sequelize.query(sql, {
          replacements: {
            vacancy_group_variable_id: vgv.id,
            job_group_variable_id: vgv.job_group_variable_id,
            job_vacancy_id: jobVacancyId,
          },
          type: sequelize.QueryTypes.RAW,
        });
      }),
    );
  }

  async weightingMatchRate(jobVacancyId) {
    const sql = `
      WITH user_weighted_scores AS (
        SELECT uvgv.user_id,
          SUM(uvgv.average_match_score * vgv.weight)
            FILTER(WHERE vgv.match_type = 'weight')
            AS match_rate,
          COUNT(*) FILTER(
            WHERE vgv.match_type = 'filter'
            AND uvgv.average_match_score < 100
          ) AS below_threshold_count
        FROM vacancy_group_variables vgv
        JOIN user_vacancy_group_variables uvgv ON vgv.id = uvgv.vacancy_group_variable_id
        WHERE vgv.job_vacancy_id = :job_vacancy_id
          AND vgv.deleted_at IS NULL
          AND uvgv.deleted_at IS NULL
        GROUP BY uvgv.user_id
      )

      INSERT INTO user_job_vacancies (
        user_id,
        job_vacancy_id,
        match_rate,
        status,
        created_at,
        updated_at
      )

      SELECT
        uws.user_id,
        :job_vacancy_id AS job_vacancy_id,
        uws.match_rate,
        CASE
          WHEN uws.match_rate >= 85
            AND uws.below_threshold_count = 0
            THEN 'matched'
          ELSE 'not_matched'
        END AS status,
        NOW() AS created_at,
        NOW() AS updated_at
      FROM user_weighted_scores uws

      ON CONFLICT (user_id, job_vacancy_id)
      DO UPDATE SET match_rate = EXCLUDED.match_rate,
        status = EXCLUDED.status,
        updated_at = EXCLUDED.updated_at;
    `;

    await sequelize.query(sql, {
      replacements: { job_vacancy_id: jobVacancyId },
      type: sequelize.QueryTypes.RAW,
    });
  }

  async getUjvCounts(jobVacancyIds) {
    if (jobVacancyIds.length === 0) {
      return {};
    }

    const countSql = `
      SELECT job_vacancy_id,
        status,
        COUNT(*) AS ujv_count
      FROM user_job_vacancies
      WHERE job_vacancy_id IN (:job_vacancy_ids)
        AND deleted_at IS NULL
      GROUP BY job_vacancy_id, status;
    `;

    const countResult = await sequelize.query(countSql, {
      replacements: { job_vacancy_ids: jobVacancyIds },
      type: sequelize.QueryTypes.SELECT,
    });

    const countsById = jobVacancyIds.reduce((acc, jobVacancyId) => {
      acc[jobVacancyId] = {
        matched: 0,
        shortlisted: 0,
        approved: 0,
        appointed: 0,
        not_matched: 0,
      };
      return acc;
    }, {});

    countResult.forEach(count => {
      if (countsById[count.job_vacancy_id]) {
        countsById[count.job_vacancy_id][count.status] = count.ujv_count;
      }
    });

    return countsById;
  }

  async upsertVacancyWorkAreas(jobVacancyId, workAreaIds) {
    if (!workAreaIds) {
      return;
    }

    await JobVacancyWorkArea.destroy({
      where: { job_vacancy_id: jobVacancyId },
    });

    const records = workAreaIds.map(workAreaId => ({
      job_vacancy_id: jobVacancyId,
      work_area_id: workAreaId,
      deleted_at: null,
    }));

    await JobVacancyWorkArea.bulkCreate(records, {
      updateOnDuplicate: ['deleted_at'],
    });
  }
}

module.exports = JobVacancyService;
