const CalculateUserVacancyVariableScoresJob = require('../../src/jobs/CalculateUserVacancyVariableScoresJob');
const SetVacancyGroupVariablesJob = require('../../src/jobs/SetVacancyGroupVariablesJob');

const {
  VacancyGroupVariable,
  UserJobVacancy,
  JobVacancyVariableBenchmark,
} = require('../../src/models');

const {
  UserFactory,
  JobVacancyFactory,
  JobGroupVariableFactory,
  JobVariableFactory,
  UserJobVariableFactory,
} = require('../factories');

jest.mock('../../src/services/OnetService');
jest.mock('../../src/services/external/GoogleAiService');

describe('CalculateUserVacancyVariableScoresJob', () => {
  let jobVacancy;

  let userBenchmark;
  let userTargetA;
  let userTargetB;

  let jgvCognitive;
  let jvCognitiveIq;
  let jvCognitiveGti;

  let jgvSocial;
  let jvSocialEq;
  let jvSocialDisc;
  let jvSocialInv;

  beforeEach(async () => {
    userBenchmark = await UserFactory.create();
    userTargetA = await UserFactory.create();
    userTargetB = await UserFactory.create();

    jobVacancy = await JobVacancyFactory.create({
      related_user_ids: [userBenchmark.id],
    });

    jgvCognitive = await JobGroupVariableFactory.create({ name: 'Cognitive' });
    jvCognitiveIq = await JobVariableFactory.create({
      job_group_variable_id: jgvCognitive.id,
      name: 'IQ',
      variable_type: 'numeric',
      mandatory: true,
    });
    jvCognitiveGti = await JobVariableFactory.create({
      job_group_variable_id: jgvCognitive.id,
      name: 'GTI',
      variable_type: 'numeric',
    });

    jgvSocial = await JobGroupVariableFactory.create({ name: 'Social' });
    jvSocialEq = await JobVariableFactory.create({
      job_group_variable_id: jgvSocial.id,
      name: 'EQ',
      variable_type: 'numeric',
    });
    jvSocialDisc = await JobVariableFactory.create({
      job_group_variable_id: jgvSocial.id,
      name: 'DISC',
      variable_type: 'categorical',
    });
    jvSocialInv = await JobVariableFactory.create({
      job_group_variable_id: jgvSocial.id,
      name: 'Influence',
      variable_type: 'numeric',
      scoring_direction: 'desc',
    });

    const userScores = {
      [userBenchmark.id]: [90, 80, 70, 'Influence', 60],
      [userTargetA.id]: [0, 70, 80, 'Influence', 80],
      [userTargetB.id]: [80, 90, 60, 'Dominance', 40],
    };

    const jvIndexes = {
      0: jvCognitiveIq.id,
      1: jvCognitiveGti.id,
      2: jvSocialEq.id,
      3: jvSocialDisc.id,
      4: jvSocialInv.id,
    };

    await Promise.all(
      Object.keys(userScores).map(async userId => {
        await Promise.all(
          userScores[userId].map(async (score, index) => {
            let normalizedValue;
            if (index === 3) normalizedValue = score == 'Influence' ? 7 : 0;
            else normalizedValue = score / 10;

            await UserJobVariableFactory.create({
              user_id: userId,
              job_variable_id: jvIndexes[index],
              raw_value: score.toString(),
              normalized_value: normalizedValue,
            });
          }),
        );
      }),
    );

    await VacancyGroupVariable.bulkCreate([
      {
        job_vacancy_id: jobVacancy.id,
        job_group_variable_id: jgvCognitive.id,
        keyword_match_count: 1,
        keyword_total_count: 1,
        match_type: 'weight',
        weight: 0.75,
        default_weight: 0.75,
      },
      {
        job_vacancy_id: jobVacancy.id,
        job_group_variable_id: jgvSocial.id,
        keyword_match_count: 1,
        keyword_total_count: 1,
        match_type: 'weight',
        weight: 0.25,
        default_weight: 0.25,
      },
    ]);

    const setVgvJob = new SetVacancyGroupVariablesJob();
    const { jvvbRecords } = await setVgvJob.generateJvvbRecords(jobVacancy);
    await JobVacancyVariableBenchmark.bulkCreate(jvvbRecords);
  });

  describe('perform', () => {
    it('should calculate user vacancy variable scores', async () => {
      const job = new CalculateUserVacancyVariableScoresJob();
      // mock config value matchRateScaleDiffThreshold to be -1
      // and matchRateThreshold to be 87
      const newConfig = {
        ...job.config,
        matchRateScaleDiffThreshold: -1,
        matchRateThreshold: 87,
      };
      job.setConfig(newConfig);
      await job.perform({ jobVacancyId: jobVacancy.id });

      // expected targetA scores:
      // IQ: 0 / 90 -> 0 -> Skipped
      // GTI: 70 / 80 ~= 87.5,
      // Avg Cognitive: (87.5) / 1 ~= 87.5

      // EQ: 80 / 70 ~= 114.29 (clamped to 100)
      // Disc: 7/7 = 100
      // Influence: 2 * 6 - 8 = 4 -> 4/6 ~= 66.67
      // Avg Social: (100 + 100 + 66.67) / 3 ~= 88.89

      // Total: 87.5 * 0.75 + 88.89 * 0.25 ~= 87.848
      const ujvA = await UserJobVacancy.findOne({ where: { user_id: userTargetA.id } });
      const roundedMatchRate = Math.round(ujvA.match_rate * 100) / 100;
      expect(roundedMatchRate).toEqual(87.85);
      expect(ujvA.status).toEqual('matched');

      // User A does not have a score fore mandatory IQ so incomplete_variables should be 1
      expect(ujvA.incomplete_variables).toEqual(1);
      // User A does not have scale that diff < -2 so filtered_out_variable_scales should be 0
      expect(ujvA.filtered_out_variable_scales).toEqual(0);

      // expected targetB scores:
      // IQ: 80 / 90 ~= 88.89
      // GTI: 90 / 80 ~= 112.5 (clamped to 100)
      // Avg Cognitive: (88.89 + 100) / 2 ~= 94.445

      // EQ: 60 / 70 ~= 85.71
      // Disc: 0/7 = 0
      // Influence: 2 * 6 - 4 = 8 -> 8/6 ~= 133.33 (clamped to 100)
      // Avg Social: (85.71 + 0 + 100) / 3 ~= 61.9

      // Total: 94.445 * 0.75 + 61.9 * 0.25 ~= 86.3125
      const ujvB = await UserJobVacancy.findOne({ where: { user_id: userTargetB.id } });
      const roundedMatchRateB = Math.round(ujvB.match_rate * 100) / 100;
      expect(roundedMatchRateB).toEqual(86.31);
      expect(ujvB.status).toEqual('not_matched');

      // User B has all mandatory scores, so incomplete_variables should be 0
      expect(ujvB.incomplete_variables).toEqual(0);
      // User B as one jv with scale diff < -2 so filtered_out_variable_scales should be 1
      expect(ujvB.filtered_out_variable_scales).toEqual(1);

      // Because userBenchmark is a benchmark, he/she should have 100 for all scores
      const ujvBench = await UserJobVacancy.findOne({ where: { user_id: userBenchmark.id } });
      expect(ujvBench.match_rate).toEqual(100);
      expect(ujvBench.status).toEqual('matched');
      expect(ujvBench.incomplete_variables).toEqual(0);
      expect(ujvBench.filtered_out_variable_scales).toEqual(0);
    });
  });
});
