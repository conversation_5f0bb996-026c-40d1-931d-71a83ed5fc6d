const GenerateVgvService = require('../../../src/services/JobVacancy/GenerateVgvService');
const { JobVacancyFactory, BoneFactory, JobGroupVariableFactory } = require('../../factories');

describe('GenerateVgvService', () => {
  let service;
  let vacancy;
  let jobGroupVariables;
  let ksao;
  let bone;

  const setWeightsCalculation = (svc, calculationMethod) => {
    svc.config.vacancyGroupVariable.defaultWeightsCalculation = calculationMethod;
  };

  beforeEach(async () => {
    service = new GenerateVgvService({
      googleAiService: {
        generateContent: jest.fn(),
      },
      onetService: {
        getOccupations: jest.fn(),
      },
    });

    vacancy = await JobVacancyFactory.create();
    bone = await BoneFactory.create({ name: 'Business' });

    ksao = {
      key_responsibilities: ['CR SOC'],
      competencies: ['CCPS AST'],
      qualifications: ['LI MD'],
    };

    const jgvCodes = ['CR', 'SOC', 'CCPS', 'AST', 'LI', 'MD', 'CIO', 'CVF'];
    jobGroupVariables = await Promise.all(
      jgvCodes.map(code => {
        return JobGroupVariableFactory.create({
          name: code,
          description: code,
          keywords: [code.toLowerCase()],
          phrases: 'test',
          jgv_code: code,
        });
      }),
    );
  });

  describe('#generateVgvRecords', () => {
    beforeEach(() => {
      jest.spyOn(GenerateVgvService.prototype, 'generatePhrases').mockResolvedValue([
        { section: 'KR', phrase: 'record-keeping', gv: 'CR', strength: 'strong', confidence: 0.95 },
        { section: 'KR', phrase: 'documentation', gv: 'CR', strength: 'medium', confidence: 0.9 },
        { section: 'KR', phrase: 'interviews', gv: 'SOC', strength: 'strong', confidence: 0.95 },
        { section: 'KR', phrase: 'communication', gv: 'SOC', strength: 'strong', confidence: 0.85 },
        { section: 'KR', phrase: 'regulations', gv: 'CVF', strength: 'strong', confidence: 0.9 },
        { section: 'SC', phrase: 'attention', gv: 'CR', strength: 'strong', confidence: 0.98 },
        { section: 'SC', phrase: 'communication', gv: 'SOC', strength: 'medium', confidence: 0.8 },
        { section: 'SC', phrase: 'problem', gv: 'CCPS', strength: 'strong', confidence: 0.9 },
        { section: 'SC', phrase: 'teamwork', gv: 'SOC', strength: 'medium', confidence: 0.85 },
        { section: 'Q', phrase: 'labor', gv: 'CVF', strength: 'medium', confidence: 0.85 },
        { section: 'Q', phrase: 'Office', gv: 'CR', strength: 'medium', confidence: 0.8 },
        { section: 'SC', phrase: 'adaptability', gv: 'AST', strength: 'strong', confidence: 0.85 },
      ]);
    });

    it('should generate vgv records correctly with dirichlet phrases', async () => {
      setWeightsCalculation(service, 'dirichlet_phrases');

      const vgvRecords = await service.generateVgvRecords({
        ksao,
        jobVacancyId: vacancy.id,
        boneId: bone.id,
        jgvAvgScales: jobGroupVariables.reduce((acc, jgv) => {
          acc[jgv.id] = 4;
          return acc;
        }, {}),
      });

      expect(vgvRecords).toBeDefined();
      expect(vgvRecords).toHaveLength(8);

      const expectedWeights = {
        SOC: 0.292,
        CR: 0.284,
        CVF: 0.135,
        CCPS: 0.111,
        AST: 0.106,
        LI: 0.024,
        MD: 0.024,
        CIO: 0.024,
      };

      vgvRecords.forEach(record => {
        const roundedWeight = Math.round(record.weight * 1000) / 1000;
        const currentJgv = jobGroupVariables.find(jgv => jgv.id === record.job_group_variable_id);
        expect(roundedWeight).toEqual(expectedWeights[currentJgv.jgv_code]);

        const defaultWeight = Math.round(record.default_weight * 1000) / 1000;
        expect(defaultWeight).toEqual(roundedWeight);

        // 'CIO', 'CVF' didn't have any matching keywords
        const isCioOrCvf = ['CIO', 'CVF'].includes(currentJgv.name);
        const expectedMatchCount = isCioOrCvf ? 0 : 1;
        expect(record.keyword_match_count).toEqual(expectedMatchCount);
        expect(record.keyword_total_count).toEqual(1);

        // CCPS, AST, MD, LI expected to have bone value of 7
        // SOC, CR, CVF expected to have bone value of 6
        // CIO expected to have bone value of 5
        const isCio = currentJgv.name === 'CIO';
        const isSocOrCrOrCvf = ['SOC', 'CR', 'CVF'].includes(currentJgv.name);
        const expectedBoneValue = isCio ? 5 : isSocOrCrOrCvf ? 6 : 7;
        expect(record.bone_value).toEqual(expectedBoneValue);
      });
    });

    it('should generate vgv records correctly with default weights', async () => {
      setWeightsCalculation(service, 'default');

      const vgvRecords = await service.generateVgvRecords({
        ksao,
        jobVacancyId: vacancy.id,
        boneId: bone.id,
        jgvAvgScales: jobGroupVariables.reduce((acc, jgv) => {
          acc[jgv.id] = 4;
          return acc;
        }, {}),
      });

      expect(vgvRecords).toBeDefined();
      expect(vgvRecords).toHaveLength(8);

      vgvRecords.forEach(record => {
        expect(record.weight).toEqual(0.125);
        expect(record.default_weight).toEqual(0.125);
      });
    });

    it('should generate only vgv that have avg baseline scale', async () => {
      const jgvAvgScales = jobGroupVariables.reduce((acc, jgv) => {
        acc[jgv.id] = 4;
        return acc;
      }, {});
      delete jgvAvgScales[jobGroupVariables[0].id];

      const vgvRecords = await service.generateVgvRecords({
        ksao,
        jobVacancyId: vacancy.id,
        boneId: bone.id,
        jgvAvgScales,
      });

      expect(vgvRecords).toBeDefined();
      expect(vgvRecords).toHaveLength(7);

      // 1/7 ~= 0.1428...
      const roundThree = num => Math.round(num * 1000) / 1000;

      vgvRecords.forEach(record => {
        expect(record.avg_baseline_scale).toEqual(4);
        expect(roundThree(record.weight)).toEqual(0.143);
        expect(roundThree(record.default_weight)).toEqual(0.143);
      });
    });
  });
});
