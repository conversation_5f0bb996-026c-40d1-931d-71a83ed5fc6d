'use strict';

const { DataTypes } = require('../config/sequelize');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('users', 'supervisor_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('users', 'hierarchy_path', {
      type: DataTypes.LTREE,
    });

    await queryInterface.addIndex('users', ['supervisor_id'], {
      name: 'users_supervisor_id_index',
    });

    await queryInterface.addIndex('users', ['hierarchy_path'], {
      name: 'users_hierarchy_path_index',
      using: 'GIST',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('users', 'supervisor_id');
    await queryInterface.removeColumn('users', 'hierarchy_path');
  },
};
