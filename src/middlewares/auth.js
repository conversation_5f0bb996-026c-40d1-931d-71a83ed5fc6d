const jwt = require('jsonwebtoken');
const { User } = require('../models');
const UnauthorizedError = require('../errors/UnauthorizedError');
const ForbiddenError = require('../errors/ForbiddenError');
const rolePermissionCache = new (require('../caches/RolePermissionCache'))();

/**
 * Verify JWT token and attach user to request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new UnauthorizedError('Access token required');
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Find user
    const user = await User.findByPk(decoded.userId);
    if (!user) {
      throw new UnauthorizedError('Invalid token');
    }

    // Attach user to request
    req.user = user;
    req.authToken = token;

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return next(new UnauthorizedError('Invalid token'));
    }
    if (error.name === 'TokenExpiredError') {
      return next(new UnauthorizedError('Token has expired'));
    }
    next(error);
  }
};

/**
 * Require permission before accessing route
 * @param {string} permission - Required permission or '*' for all permissions
 * @returns {Function} Express middleware function
 */
const requirePermission = permission => {
  return async (req, res, next) => {
    if (!req.user) {
      return next(new UnauthorizedError('Authentication required'));
    }

    const userRole = req.user.role;
    const rolePermissions = await rolePermissionCache.getRolePermissions(userRole);
    req.permissions = rolePermissions;
    if (permission === '*') return next();

    const isAuthorized = rolePermissions.includes(permission);
    if (!isAuthorized) return next(new ForbiddenError('Insufficient permissions'));

    next();
  };
};

/**
 * Optional authentication - attach user if token is valid
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return next(); // No token, continue without user
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.userId);

    if (user) {
      req.user = user;
    }

    next();
  } catch (error) {
    // Invalid token, continue without user
    console.error('Optional auth error:', error);
    next();
  }
};

module.exports = {
  authenticateToken,
  requirePermission,
  optionalAuth,
};
