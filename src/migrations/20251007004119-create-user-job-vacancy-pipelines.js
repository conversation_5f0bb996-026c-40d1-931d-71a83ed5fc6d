'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_job_vacancy_pipelines', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      job_vacancy_pipeline_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_vacancy_pipelines',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      created_by_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      approved_by_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      approved_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      moved_to_next_pipeline_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      note: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    // Add indexes
    await queryInterface.addIndex('user_job_vacancy_pipelines', ['user_id'], {
      name: 'user_job_vacancy_pipelines_user_id_index',
    });

    await queryInterface.addIndex('user_job_vacancy_pipelines', ['job_vacancy_pipeline_id'], {
      name: 'user_job_vacancy_pipelines_job_vacancy_pipeline_id_index',
    });

    await queryInterface.addIndex('user_job_vacancy_pipelines', ['created_by_id'], {
      name: 'user_job_vacancy_pipelines_created_by_id_index',
    });

    await queryInterface.addIndex('user_job_vacancy_pipelines', ['approved_by_id'], {
      name: 'user_job_vacancy_pipelines_approved_by_id_index',
    });

    // Add unique constraint for user_id and job_vacancy_pipeline_id
    await queryInterface.addIndex(
      'user_job_vacancy_pipelines',
      ['user_id', 'job_vacancy_pipeline_id'],
      {
        unique: true,
        name: 'user_job_vacancy_pipelines_user_id_job_vacancy_pipeline_id_unique',
      },
    );
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('user_job_vacancy_pipelines');
  },
};
