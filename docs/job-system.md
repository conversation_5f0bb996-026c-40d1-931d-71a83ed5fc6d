# Job System Documentation

## Overview

The Paragon API uses BullMQ for background job processing, providing a
Sidekiq-like interface for enqueueing and processing jobs. This system handles
long-running tasks asynchronously, improving API response times and user
experience.

## Architecture

### Components

1. **ApplicationJob** - Base class for all jobs with Sidekiq-like interface and
   configurable retry mechanisms
2. **JobWorker** - Main worker process that processes jobs from the Redis queue
   (`src/JobWorker.js`)
3. **WorkerHealthServer** - Health check server for Kubernetes probes
   (`src/WorkerHealthServer.js`)
4. **Job Classes** - Specific job implementations (e.g., SetJobDescriptionJob,
   SetVacancyGroupVariablesJob)
5. **Redis** - Message broker and job storage with centralized connection
   management (`src/config/redis.js`)

### Queue Structure

- **Single Queue**: `default` - All jobs are processed through this queue
- **Priority Support**: Jobs can be assigned priorities (lower number = higher
  priority)
- **Concurrency**: Configurable concurrent job processing (default: 5)
- **Retry System**: Configurable retry strategies (exponential, fixed, linear
  backoff)

## Configuration

### Environment Variables

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_RETRY_DELAY=100

# Queue Configuration
QUEUE_REMOVE_ON_COMPLETE=100
QUEUE_REMOVE_ON_FAIL=50
QUEUE_DEFAULT_ATTEMPTS=3
QUEUE_BACKOFF_TYPE=exponential
QUEUE_BACKOFF_DELAY=2000
QUEUE_BACKOFF_MAX_DELAY=30000
QUEUE_CONCURRENCY=10
QUEUE_STALLED_INTERVAL=30000
QUEUE_MAX_STALLED_COUNT=1

# Worker Configuration
WORKER_HEALTH_PORT=3001
WORKER_ENABLE_HEALTH_SERVER=true
```

### Configuration File

The job system configuration is defined in `src/config/config.js`:

```javascript
redis: {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT, 10) || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB, 10) || 0,
  maxRetriesPerRequest: null, // Required for BullMQ blocking operations
  retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY, 10) || 100,
  lazyConnect: true,
},

queue: {
  defaultJobOptions: {
    removeOnComplete: parseInt(process.env.QUEUE_REMOVE_ON_COMPLETE, 10) || 100,
    removeOnFail: parseInt(process.env.QUEUE_REMOVE_ON_FAIL, 10) || 50,
    attempts: parseInt(process.env.QUEUE_DEFAULT_ATTEMPTS, 10) || 3,
    backoff: {
      type: process.env.QUEUE_BACKOFF_TYPE || 'exponential', // exponential, fixed, linear
      delay: parseInt(process.env.QUEUE_BACKOFF_DELAY, 10) || 2000,
      settings: {
        maxDelay: parseInt(process.env.QUEUE_BACKOFF_MAX_DELAY, 10) || 30000,
      },
    },
  },
  concurrency: parseInt(process.env.QUEUE_CONCURRENCY, 10) || 10,
  stalledInterval: parseInt(process.env.QUEUE_STALLED_INTERVAL, 10) || 30000,
  maxStalledCount: parseInt(process.env.QUEUE_MAX_STALLED_COUNT, 10) || 1,
},

worker: {
  healthPort: parseInt(process.env.WORKER_HEALTH_PORT, 10) || 3001,
  enableHealthServer: process.env.WORKER_ENABLE_HEALTH_SERVER !== 'false',
}
```

## Creating Jobs

### 1. Create a Job Class

Create a new job class in `src/jobs/` that extends `ApplicationJob`:

```javascript
const ApplicationJob = require('./ApplicationJob');

class MyCustomJob extends ApplicationJob {
  /**
   * Configure retry behavior for this job
   * @returns {Object} Retry configuration
   */
  getRetryConfig() {
    return {
      attempts: 5, // Custom retry attempts
      backoff: ApplicationJob.createBackoffStrategy('exponential', 1000, 10000),
    };
  }

  /**
   * Main job processing method
   * @param {Object} data - Job data
   * @returns {Promise<any>} Job result
   */
  async perform(data) {
    const { param1, param2 } = data;

    // Your job logic here
    console.log(`Processing job with params: ${param1}, ${param2}`);

    // Return result
    return { status: 'completed', processedAt: new Date() };
  }

  /**
   * Optional: Hook called before job processing
   */
  async before_perform(data) {
    console.log(`Starting MyCustomJob with data:`, data);
  }

  /**
   * Optional: Hook called after successful job processing
   */
  async after_perform(data, result) {
    console.log(`Completed MyCustomJob:`, result);
  }

  /**
   * Optional: Hook called when job fails
   */
  async on_failure(data, error) {
    console.error(`MyCustomJob failed:`, error);
  }

  /**
   * Optional: Custom retry logic
   */
  shouldRetry(error, attemptsMade, maxAttempts) {
    // Don't retry validation errors
    if (error.message.includes('validation')) {
      return false;
    }
    return attemptsMade < maxAttempts;
  }
}

module.exports = MyCustomJob;
```

### 2. Register the Job

Jobs are automatically registered through `src/jobs/index.js`. Simply create
your job file in the `src/jobs/` directory, and it will be automatically loaded
by the worker.

## Retry System

### Built-in Retry Strategies

The job system supports configurable retry mechanisms:

#### 1. Exponential Backoff (Default)

```javascript
getRetryConfig() {
  return {
    attempts: 5,
    backoff: ApplicationJob.createBackoffStrategy('exponential', 1000, 10000),
    // Results in: 1s, 2s, 4s, 8s, 10s (capped at maxDelay)
  };
}
```

#### 2. Fixed Delay

```javascript
getRetryConfig() {
  return {
    attempts: 3,
    backoff: ApplicationJob.createBackoffStrategy('fixed', 5000),
    // Results in: 5s, 5s, 5s
  };
}
```

#### 3. Linear Backoff

```javascript
getRetryConfig() {
  return {
    attempts: 4,
    backoff: ApplicationJob.createBackoffStrategy('linear', 2000),
    // Results in: 2s, 4s, 6s, 8s
  };
}
```

### Custom Retry Logic

Implement `shouldRetry()` for custom retry decision logic:

```javascript
shouldRetry(error, attemptsMade, maxAttempts) {
  // Don't retry validation errors
  if (error.message.includes('validation')) {
    return false;
  }

  // Don't retry after 3 attempts for rate limit errors
  if (error.message.includes('rate limit') && attemptsMade >= 3) {
    return false;
  }

  return attemptsMade < maxAttempts;
}
```

## Worker Process

### JobWorker Class

The main worker process (`src/JobWorker.js`) features:

- **Unique Worker ID**: Generated with hostname-PID-random for cluster
  identification
- **Graceful Shutdown**: Proper signal handling with timeout mechanisms
- **Health Checks**: Integrated health server for Kubernetes probes
- **Job Statistics**: Tracking processed and failed job counts
- **Automatic Job Registration**: Jobs are loaded from `src/jobs/index.js`

### WorkerHealthServer

Provides health check endpoints for Kubernetes:

- `GET /health` - Overall health status with worker statistics
- `GET /ready` - Readiness probe (returns 503 during shutdown)
- `GET /live` - Liveness probe

Example health response:

```json
{
  "status": "healthy",
  "workerId": "hostname-12345-abc123",
  "uptime": 3600000,
  "processedJobs": 150,
  "failedJobs": 2,
  "timestamp": "2025-09-15T10:00:00.000Z",
  "redis": { "connected": true },
  "server": { "shuttingDown": false }
}
```

## Enqueueing Jobs

### Basic Usage

```javascript
const MyCustomJob = require('../jobs/MyCustomJob');

// Enqueue a job
await MyCustomJob.perform_async({
  param1: 'value1',
  param2: 'value2',
});
```

### With Options

```javascript
await MyCustomJob.perform_async(
  { param1: 'value1', param2: 'value2' },
  {
    priority: 1, // Higher priority (lower number = higher priority)
    delay: 5000, // Delay execution by 5 seconds
    attempts: 5, // Override default retry attempts
    removeOnComplete: 10, // Keep only 10 completed jobs
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
);
```

## Running the Worker

### Development

```bash
# Start worker in production mode
npm run worker

# For development with debugging
DEBUG=bullmq* npm run worker
```

### Production Deployment

The worker is deployed as a separate Kubernetes deployment with:

- **Health Checks**: Kubernetes probes monitor worker health via port 3001
- **Graceful Shutdown**: Proper signal handling for zero-downtime deployments
- **Resource Limits**: Configured CPU and memory limits
- **Auto-restart**: Kubernetes automatically restarts failed workers

```yaml
# k8s/worker-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paragon-api-worker
spec:
  replicas: 2
  template:
    spec:
      containers:
        - name: worker
          image: paragon-api:latest
          command: ['npm', 'run', 'worker']
          ports:
            - containerPort: 3001
              name: health
          livenessProbe:
            httpGet:
              path: /live
              port: 3001
          readinessProbe:
            httpGet:
              path: /ready
              port: 3001
```

## Monitoring

### Worker Logs

The worker provides detailed logging with unique worker identification:

```
🚀 Starting BullMQ worker [hostname-12345-abc123]...
📊 Worker configuration:
   - Concurrency: 10
   - Hostname: hostname
   - PID: 12345
   - Node version: v22.12.0
🏥 Starting health check server for Kubernetes probes...
🏥 Worker health check server listening on port 3001
🚀 Worker [hostname-12345-abc123] started successfully
📊 Configuration: concurrency=10, stalledInterval=30s
📋 Registered job types: ['SetJobDescriptionJob', 'SetVacancyGroupVariablesJob']
⏳ Waiting for jobs...
✅ Redis connected successfully

[hostname-12345-abc123] Processing job: SetJobDescriptionJob (ID: 42, Attempt: 1/3) with data: { vacancy_id: 123 }
🚀 Starting SetJobDescriptionJob with data: { vacancy_id: 123 }
✅ SetJobDescriptionJob completed: Job description generated successfully.
🎉 SetJobDescriptionJob finished successfully!
[hostname-12345-abc123] ✅ Job SetJobDescriptionJob (42) completed in 2500ms
```

### Health Monitoring

Monitor worker health via HTTP endpoints:

```bash
# Check overall health
curl http://localhost:3001/health

# Check readiness (for load balancers)
curl http://localhost:3001/ready

# Check liveness (for restart decisions)
curl http://localhost:3001/live
```

## Error Handling

### Automatic Retries

Jobs automatically retry on failure based on configuration:

- **Default attempts**: 3 with exponential backoff
- **Configurable strategies**: exponential, fixed, linear backoff
- **Custom retry logic**: Implement `shouldRetry()` for custom decisions
- **Failed job retention**: Configurable cleanup of failed jobs

Example retry sequence with exponential backoff:

```
[worker-123] ❌ Job MyJob (42) failed: Network timeout (will retry, attempt 2/3)
[worker-123] Processing job: MyJob (ID: 42, Attempt: 2/3)
[worker-123] ❌ Job MyJob (42) failed: Network timeout (will retry, attempt 3/3)
[worker-123] Processing job: MyJob (ID: 42, Attempt: 3/3)
[worker-123] ✅ Job MyJob (42) completed successfully
```

### Custom Error Handling

Implement hooks in your job class for comprehensive error handling:

```javascript
async on_failure(data, error) {
  // Log detailed error information
  console.error(`Job failed with data:`, data);
  console.error(`Error:`, error.message);

  // Send notifications for critical failures
  if (error.message.includes('critical')) {
    await this.notifyAdmins(error, data);
  }

  // Update external systems
  await this.updateExternalStatus(data.id, 'failed');
}

// Custom retry decision logic
shouldRetry(error, attemptsMade, maxAttempts) {
  // Don't retry validation errors
  if (error.message.includes('validation')) {
    console.log('❌ Not retrying validation error');
    return false;
  }

  // Don't retry permanent failures
  if (error.statusCode === 404) {
    console.log('❌ Not retrying 404 error');
    return false;
  }

  const shouldRetry = attemptsMade < maxAttempts;
  console.log(`${shouldRetry ? '🔄' : '❌'} ${shouldRetry ? 'Will retry' : 'Max attempts reached'}`);

  return shouldRetry;
}
```

## Best Practices

### 1. Job Design

- **Keep jobs idempotent**: Jobs should be safe to run multiple times
- **Use specific job classes**: Create focused job classes for different tasks
- **Pass minimal data**: Use IDs rather than full objects to reduce memory usage
- **Handle edge cases**: Validate input data and handle missing dependencies
- **Implement proper logging**: Use consistent logging for debugging and
  monitoring

### 2. Error Handling

- **Always implement error handling**: Use `on_failure` hooks for cleanup
- **Use custom retry logic**: Implement `shouldRetry()` for intelligent retry
  decisions
- **Log errors with context**: Include job data and stack traces for debugging
- **Handle different error types**: Distinguish between retriable and permanent
  failures
- **Monitor failed jobs**: Set up alerts for critical job failures

### 3. Performance

- **Optimize concurrency**: Balance worker concurrency with system resources
- **Monitor Redis memory**: Track job data and clean up completed jobs
- **Use job priorities**: Prioritize time-sensitive jobs appropriately
- **Batch related operations**: Group similar operations to reduce overhead
- **Profile job performance**: Monitor job execution times and optimize
  bottlenecks

### 4. Testing

- **Mock external services**: Use mocks for reliable test execution
- **Test job logic independently**: Test job methods separately from queue
  mechanics
- **Use test-specific Redis**: Isolate tests with dedicated Redis databases
- **Test retry scenarios**: Verify retry logic with simulated failures
- **Test error handling**: Ensure `on_failure` hooks work correctly

### 5. Production Deployment

- **Use health checks**: Implement proper Kubernetes health probes
- **Configure resource limits**: Set appropriate CPU and memory limits
- **Monitor worker metrics**: Track job processing rates and error rates
- **Plan for graceful shutdowns**: Ensure workers handle SIGTERM/SIGINT properly
- **Scale based on queue size**: Monitor queue depth and scale workers
  accordingly

## Existing Jobs

### SetJobDescriptionJob

Generates AI-powered job descriptions for job vacancies using ONET data and
Google AI.

**Purpose**: Automatically create comprehensive job descriptions to improve
vacancy quality

**Data**: `{ vacancy_id: number }`

**Dependencies**:

- OnetService (occupational data)
- GoogleAiService (AI text generation)
- QdrantClient (vector database)
- GenerateJobDescService (orchestration)

**Process**:

1. Fetches job vacancy data from database
2. Retrieves relevant ONET occupational information
3. Generates job description using Google AI
4. Updates vacancy record with generated description
5. Handles errors and retries for external service failures

**Usage**:

```javascript
await SetJobDescriptionJob.perform_async({ vacancy_id: 123 });
```

### SetVacancyGroupVariablesJob

Generates KSAO (Knowledge, Skills, Abilities, Other) profiles and matches them
with organizational job group variables.

**Purpose**: Automatically align job requirements with organizational competency
frameworks

**Data**: `{ vacancy_id: number }`

**Dependencies**:

- AI services for KSAO profile generation
- Job group variable matching algorithms
- Database models for vacancy and group data

**Process**:

1. Fetches job vacancy information
2. Generates KSAO profile using AI analysis
3. Matches KSAO requirements with job group variables
4. Updates vacancy with competency alignment data
5. Handles complex data relationships and validation

**Usage**:

```javascript
await SetVacancyGroupVariablesJob.perform_async({ vacancy_id: 456 });
```

## Troubleshooting

### Common Issues

1. **Redis Connection Errors**

   ```
   Error: Connection refused
   ```

   - **Solution**: Check Redis server is running with `redis-cli ping`
   - **Solution**: Verify connection settings in environment variables
   - **Solution**: Check network connectivity and firewall rules

2. **Jobs Not Processing**

   ```
   ⏳ Waiting for jobs... (no activity)
   ```

   - **Solution**: Ensure worker is running with `npm run worker`
   - **Solution**: Check worker logs for startup errors
   - **Solution**: Verify jobs are being enqueued to the correct queue
   - **Solution**: Check Redis queue contents with
     `redis-cli LLEN bull:default:waiting`

3. **Worker Shutdown Hanging**

   ```
   ^C (process doesn't exit)
   ```

   - **Solution**: This is now fixed with timeout-based graceful shutdown
   - **Solution**: Worker automatically force-exits after 8 seconds
   - **Solution**: Use `kill -9 <pid>` as last resort for stuck processes

4. **Memory Issues**

   ```
   Redis memory usage growing continuously
   ```

   - **Solution**: Monitor Redis memory with `redis-cli INFO memory`
   - **Solution**: Adjust job retention settings (`removeOnComplete`,
     `removeOnFail`)
   - **Solution**: Consider Redis persistence and eviction policies
   - **Solution**: Clean up old jobs regularly

5. **Health Check Failures**

   ```
   Kubernetes probe failures
   ```

   - **Solution**: Check health server is running on port 3001
   - **Solution**: Verify health endpoints return proper responses
   - **Solution**: Check for port conflicts with other services

### Debugging

1. **Enable Debug Logging**

   ```bash
   DEBUG=bullmq* npm run worker
   ```

   This provides detailed BullMQ internals logging.

2. **Check Worker Health**

   ```bash
   curl -s http://localhost:3001/health | jq
   ```

   Monitor worker status and statistics.

3. **Monitor Queue State**

   ```bash
   # Check waiting jobs
   redis-cli LLEN bull:default:waiting

   # Check active jobs
   redis-cli LLEN bull:default:active

   # Check failed jobs
   redis-cli LLEN bull:default:failed

   # List all queue keys
   redis-cli KEYS bull:default:*
   ```

4. **View Job Data**

   ```bash
   # Get specific job data
   redis-cli HGET bull:default:42 data

   # List recent job IDs
   redis-cli LRANGE bull:default:completed -10 -1
   ```

5. **Clean Queue Data**

   ```javascript
   const { Queue } = require('bullmq');
   const queue = new Queue('default', { connection: redisConfig.connection });

   // Clean old completed jobs
   await queue.clean(24 * 60 * 60 * 1000, 1000, 'completed');

   // Clean old failed jobs
   await queue.clean(7 * 24 * 60 * 60 * 1000, 1000, 'failed');
   ```

### Performance Monitoring

Monitor these key metrics in production:

- **Queue Depth**: Number of waiting/active jobs
- **Job Processing Rate**: Jobs completed per minute
- **Job Success Rate**: Ratio of successful to failed jobs
- **Worker Memory Usage**: RSS memory consumption
- **Redis Memory Usage**: Total memory used by job data
- **Average Job Duration**: Time to complete jobs
- **Retry Rate**: Percentage of jobs requiring retries
