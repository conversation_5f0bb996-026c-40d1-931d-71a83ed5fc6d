const express = require('express');
const resources = require('./resources');
const userJobVacanciesController = require('../controllers/UserJobVacanciesController');
const { authenticateToken, requirePermission } = require('../middlewares/auth');

const router = express.Router();
router.use(authenticateToken);

router.use(
  '/',
  resources('user_job_vacancies', userJobVacanciesController, { only: ['index', 'show'] }),
);

// Add custom PATCH route for updating pipelines
router.patch(
  '/',
  requirePermission('user_job_vacancies:update'),
  userJobVacanciesController.update,
);

module.exports = router;
