{"name": "paragon-api", "version": "1.0.0", "description": "", "license": "ISC", "author": "", "type": "commonjs", "main": "index.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "worker": "node src/JobWorker.js", "worker:dev": "nodemon src/JobWorker.js", "console": "node src/console.js", "test": "jest --maxConcurrency 1 --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --bail", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --reporters=default --reporters=jest-junit", "test:setup": "node scripts/setup-test-db.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:check": "eslint . --max-warnings 0", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "db:migrate": "sequelize-cli db:migrate --debug && node src/schema-generator.js", "db:migrate:undo": "sequelize-cli db:migrate:undo && node src/schema-generator.js", "db:reset": "sequelize-cli db:migrate:undo:all && sequelize-cli db:migrate && sequelize-cli db:seed:all && node src/schema-generator.js", "db:create": "sequelize-cli db:create", "db:seed": "sequelize-cli db:seed:all", "db:migrate:generate": "sequelize-cli model:generate", "db:verify": "node src/schema-verifier.js"}, "dependencies": {"@bull-board/express": "^6.12.7", "@bull-board/ui": "^6.12.7", "@google/genai": "^1.16.0", "@qdrant/qdrant-js": "^1.15.1", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bcrypt": "^6.0.0", "bullmq": "^5.58.5", "cls-hooked": "^4.2.2", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "prom-client": "^15.1.3", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "tslib": "^2.8.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@eslint/json": "^0.13.1", "@faker-js/faker": "^9.9.0", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "globals": "^16.3.0", "husky": "^9.1.7", "ioredis-mock": "^8.13.0", "jest": "^30.0.5", "jest-junit": "^16.0.0", "lint-staged": "^16.1.5", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.1.4"}, "lint-staged": {"*.{js,mjs,cjs}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}