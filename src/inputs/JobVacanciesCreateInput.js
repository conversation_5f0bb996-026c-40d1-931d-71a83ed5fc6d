const ApplicationInput = require('./ApplicationInput');

class JobVacanciesCreateInput extends ApplicationInput {
  /**
   * Get the JSON schema for validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          nullable: true,
        },
        job_title_id: {
          type: 'integer',
          nullable: true,
        },
        department: {
          type: 'string',
        },
        job_grade: {
          type: 'string',
        },
        job_description: {
          type: 'string',
        },
        reference_user_ids: {
          type: 'array',
          items: {
            type: 'integer',
          },
          default: [],
        },
        detailed_descriptions: {
          type: 'object',
          properties: {
            key_responsibilities: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            qualifications: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            competencies: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            success_metrics: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            work_inputs: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
            work_outputs: {
              type: 'array',
              items: {
                type: 'string',
              },
              default: [],
            },
          },
        },
        job_level_id: {
          type: 'integer',
        },
        work_area_id: {
          type: 'integer',
        },
        bone_id: {
          type: 'integer',
        },
        relevant_working_experience: {
          type: 'boolean',
        },
        role_summary: {
          type: 'string',
        },
        work_area_ids: {
          type: 'array',
          items: {
            type: 'integer',
          },
          default: [],
        },
        followup_action: {
          type: 'string',
          enum: ['generate_jobdesc', 'generate_job_variables'],
          nullable: true,
        },
      },
      required: [],
      additionalProperties: false,
    };
  }

  output() {
    const data = super.output();

    data.related_user_ids = data.reference_user_ids;
    delete data.reference_user_ids;

    return data;
  }
}

module.exports = JobVacanciesCreateInput;
