const BaseRepository = require('./BaseRepository');
const { JobVacancyPipeline } = require('../models');

class JobVacancyPipelinesRepository extends BaseRepository {
  constructor() {
    super(JobVacancyPipeline);
  }

  /**
   * Filter by job vacancy ID
   * Usage: /api/v1/job_vacancies/:id/pipelines?job_vacancy_id=1
   * @param {number} jobVacancyId - Job vacancy ID
   * @returns {Object} Where condition for job_vacancy_id filter
   */
  filterByJobVacancyId(jobVacancyId) {
    const id = parseInt(jobVacancyId, 10);
    if (!id || isNaN(id)) {
      return null;
    }
    return { job_vacancy_id: id };
  }

  /**
   * Filter by parameterized name
   * Usage: /api/v1/job_vacancies/:id/pipelines?parameterized_name=shortlisted
   * @param {string} parameterizedName - Parameterized name to filter by
   * @returns {Object} Where condition for parameterized_name filter
   */
  filterByParameterizedName(parameterizedName) {
    if (!parameterizedName) return null;
    return { parameterized_name: parameterizedName };
  }

  /**
   * Filter by order level
   * Usage: /api/v1/job_vacancies/:id/pipelines?order_level=1
   * @param {number} orderLevel - Order level to filter by
   * @returns {Object} Where condition for order_level filter
   */
  filterByOrderLevel(orderLevel) {
    const level = parseInt(orderLevel, 10);
    if (isNaN(level)) return null;
    return { order_level: level };
  }
}

module.exports = JobVacancyPipelinesRepository;
