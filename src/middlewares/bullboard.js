const { BullMQAdapter } = require('@bull-board/api/bullMQAdapter');
const { ExpressAdapter } = require('@bull-board/express');
const { Queue } = require('bullmq');
const { createBullBoard } = require('@bull-board/api');

const config = require('../config/config');
const { getRedisConnection } = require('../config/redis');

const redis = getRedisConnection();
const defaultQueue = new Queue(config.queue.name, { connection: redis });

const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

createBullBoard({
  queues: [new BullMQAdapter(defaultQueue)],
  serverAdapter,
});

const bullBoardAuth = (req, res, next) => {
  const { username, password } = config.bullBoardAuth;
  if (!username || !password) {
    return next(new Error('Bull Board authentication not configured'));
  }

  const auth = { username, password };
  const b64auth = (req.headers.authorization || '').split(' ')[1] || '';
  const [user, pwd] = Buffer.from(b64auth, 'base64').toString().split(':');

  if (user && user === auth.username && pwd === auth.password) {
    return next();
  }

  res.set('WWW-Authenticate', 'Basic realm="401"');
  res.status(401).send('Authentication required.');
};

module.exports = {
  bullBoardAuth,
  bullBoardServer: serverAdapter,
};
